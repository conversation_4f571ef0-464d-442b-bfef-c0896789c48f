"use client"

import { useState, useEffect } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Progress } from "@/components/ui/progress"
import { FileText, Upload, Search, Download, Eye, Trash2, More<PERSON><PERSON>zon<PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON><PERSON> } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { DocumentService, type DocumentWithProject } from "@/lib/services/documentService"
import { ProjectService } from "@/lib/services/projectService"
import type { Project } from "@/lib/supabase"

export default function DocumentsPage() {
  const [documents, setDocuments] = useState<DocumentWithProject[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState("")
  const [projectFilter, setProjectFilter] = useState<"all" | string>("all")
  
  // Upload dialog state
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false)
  const [uploadForm, setUploadForm] = useState({
    projectId: "",
    file: null as File | null,
  })
  const [uploadProgress, setUploadProgress] = useState(0)

  const { toast } = useToast()

  useEffect(() => {
    loadInitialData()
  }, [])

  const loadInitialData = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      // Load projects and documents in parallel
      const [projectsData, documentsData] = await Promise.all([
        ProjectService.getAllProjects(),
        DocumentService.getAllDocuments()
      ])

      setProjects(projectsData)
      setDocuments(documentsData)
    } catch (error) {
      console.error('Error loading initial data:', error)
      setError('Gagal memuat data dokumen. Silakan coba lagi.')
      toast({
        title: "Error",
        description: "Gagal memuat data dokumen. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const filteredDocuments = documents.filter((doc) => {
    const matchesSearch =
      doc.file_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (doc.project_name && doc.project_name.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesProject = projectFilter === "all" || doc.project_id === projectFilter

    return matchesSearch && matchesProject
  })

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file size (max 50MB)
      if (file.size > 50 * 1024 * 1024) {
        toast({
          title: "Error",
          description: "Ukuran file maksimal 50MB.",
          variant: "destructive",
        })
        return
      }
      
      setUploadForm({ ...uploadForm, file })
    }
  }

  const handleUploadSubmit = async () => {
    if (!uploadForm.file || !uploadForm.projectId) {
      toast({
        title: "Error",
        description: "Pilih project dan file yang akan diupload.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsUploading(true)
      setUploadProgress(0)

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      const documentData = {
        project_id: uploadForm.projectId,
        file: uploadForm.file,
        uploaded_by: 'Current User' // In real app, get from auth
      }

      const newDocument = await DocumentService.createDocument(documentData)
      
      // Complete progress and add to list
      setUploadProgress(100)
      clearInterval(progressInterval)
      
      // Add project name to the new document
      const projectName = projects.find(p => p.id === uploadForm.projectId)?.name
      const documentWithProject = { ...newDocument, project_name: projectName }
      
      setDocuments(prev => [documentWithProject, ...prev])
      setUploadForm({ projectId: "", file: null })
      setIsUploadDialogOpen(false)

      toast({
        title: "Berhasil",
        description: "Dokumen berhasil diupload.",
      })
    } catch (error) {
      console.error('Error uploading document:', error)
      toast({
        title: "Error",
        description: "Gagal mengupload dokumen. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }

  const handleDeleteDocument = async (id: string, fileName: string) => {
    if (!confirm(`Apakah Anda yakin ingin menghapus dokumen "${fileName}"?`)) {
      return
    }

    try {
      await DocumentService.deleteDocument(id)
      setDocuments(prev => prev.filter(doc => doc.id !== id))

      toast({
        title: "Berhasil",
        description: "Dokumen berhasil dihapus.",
      })
    } catch (error) {
      console.error('Error deleting document:', error)
      toast({
        title: "Error",
        description: "Gagal menghapus dokumen.",
        variant: "destructive",
      })
    }
  }

  const handleDownloadDocument = async (doc: DocumentWithProject) => {
    try {
      // Open the file URL in a new tab for download
      window.open(doc.file_url, '_blank')
      
      toast({
        title: "Download dimulai",
        description: `Download ${doc.file_name} sedang dimulai.`,
      })
    } catch (error) {
      console.error('Error downloading document:', error)
      toast({
        title: "Error",
        description: "Gagal mendownload dokumen.",
        variant: "destructive",
      })
    }
  }

  const clearFilters = () => {
    setSearchTerm("")
    setProjectFilter("all")
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getFileTypeInfo = (fileName: string) => {
    return DocumentService.getFileTypeInfo(fileName)
  }

  const getDocumentStats = () => {
    const total = documents.length
    const byProject = projects.map(project => ({
      name: project.name,
      count: documents.filter(doc => doc.project_id === project.id).length
    })).filter(item => item.count > 0)

    return { total, byProject }
  }

  const stats = getDocumentStats()

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Memuat data dokumen...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}{" "}
            <Button variant="link" onClick={loadInitialData} className="p-0 h-auto">
              Coba lagi
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <>
      <header className="flex h-16 shrink-0 items-center justify-between border-b px-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <h1 className="text-2xl font-bold">Dokumentasi</h1>
        </div>
        <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Upload className="h-4 w-4 mr-2" />
              Upload Dokumen
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Upload Dokumen Baru</DialogTitle>
              <DialogDescription>Upload dokumen pendukung untuk project</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="project">Project *</Label>
                <Select
                  value={uploadForm.projectId}
                  onValueChange={(value) => setUploadForm({ ...uploadForm, projectId: value })}
                  disabled={isUploading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih project" />
                  </SelectTrigger>
                  <SelectContent>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="file">File *</Label>
                <Input
                  id="file"
                  type="file"
                  onChange={handleFileUpload}
                  disabled={isUploading}
                  accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif,.zip,.rar"
                />
                {uploadForm.file && (
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p>File terpilih: {uploadForm.file.name}</p>
                    <p>Ukuran: {(uploadForm.file.size / (1024 * 1024)).toFixed(2)} MB</p>
                  </div>
                )}
                <p className="text-xs text-muted-foreground">
                  Format yang didukung: PDF, Word, Excel, PowerPoint, gambar, zip. Maksimal 50MB.
                </p>
              </div>
              {isUploading && (
                <div className="grid gap-2">
                  <Label>Progress Upload</Label>
                  <Progress value={uploadProgress} className="w-full" />
                  <p className="text-sm text-muted-foreground text-center">{uploadProgress}%</p>
                </div>
              )}
            </div>
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => !isUploading && setIsUploadDialogOpen(false)}
                disabled={isUploading}
              >
                Batal
              </Button>
              <Button 
                onClick={handleUploadSubmit} 
                disabled={isUploading || !uploadForm.file || !uploadForm.projectId}
              >
                {isUploading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                {isUploading ? 'Mengupload...' : 'Upload'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </header>

      <div className="flex-1 p-6 space-y-6">
        {/* Statistics */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Dokumen</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Project Aktif</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.byProject.length}</div>
              <p className="text-xs text-muted-foreground">Project dengan dokumen</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Storage</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {documents.reduce((total, doc) => {
                  const size = parseFloat(doc.file_size?.replace(' MB', '') || '0')
                  return total + size
                }, 0).toFixed(1)} MB
              </div>
              <p className="text-xs text-muted-foreground">Total storage terpakai</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Filter & Pencarian</CardTitle>
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Reset Filter
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari nama file atau project..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Select value={projectFilter} onValueChange={setProjectFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Project" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Project</SelectItem>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Documents Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredDocuments.length === 0 ? (
            <div className="col-span-full">
              <Card>
                <CardContent className="text-center py-12">
                  <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Tidak ada dokumen</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm || projectFilter !== 'all'
                      ? 'Tidak ada dokumen yang sesuai dengan filter'
                      : 'Belum ada dokumen yang diupload'
                    }
                  </p>
                  {(searchTerm || projectFilter !== 'all') ? (
                    <Button variant="outline" onClick={clearFilters}>
                      Reset Filter
                    </Button>
                  ) : (
                    <Button onClick={() => setIsUploadDialogOpen(true)}>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Dokumen Pertama
                    </Button>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            filteredDocuments.map((doc) => {
              const fileInfo = getFileTypeInfo(doc.file_name)
              return (
                <Card key={doc.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <div className="text-3xl">{fileInfo.icon}</div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <h3 className="font-medium truncate" title={doc.file_name}>
                              {doc.file_name}
                            </h3>
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline" className={fileInfo.color}>
                                {fileInfo.type}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {doc.file_size}
                              </span>
                            </div>
                            <div className="mt-2 space-y-1">
                              <p className="text-sm text-muted-foreground">
                                Project: {doc.project_name}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Upload: {formatDate(doc.uploaded_at)}
                              </p>
                              {doc.uploaded_by && (
                                <p className="text-xs text-muted-foreground">
                                  Oleh: {doc.uploaded_by}
                                </p>
                              )}
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => window.open(doc.file_url, '_blank')}>
                                <Eye className="h-4 w-4 mr-2" />
                                Preview
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleDownloadDocument(doc)}>
                                <Download className="h-4 w-4 mr-2" />
                                Download
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="text-red-600"
                                onClick={() => handleDeleteDocument(doc.id, doc.file_name)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Hapus
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })
          )}
        </div>

        {/* Show filter summary if filters are applied */}
        {(searchTerm || projectFilter !== 'all') && (
          <div className="text-sm text-muted-foreground text-center py-2">
            Menampilkan {filteredDocuments.length} dari {stats.total} dokumen total
          </div>
        )}
      </div>
    </>
  )
}
