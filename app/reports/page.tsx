"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  FileText, 
  Download, 
  Printer, 
  Calendar, 
  BarChart3, 
  <PERSON><PERSON><PERSON> as PieChartIcon, 
  TrendingUp,
  Loader2,
  AlertCircle,
  FileSpreadsheet,
  RefreshCw
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import {
  BarC<PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON>A<PERSON><PERSON>,
  <PERSON>tesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Responsive<PERSON>ontaine<PERSON>,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts"
import { 
  ReportService,
  type ProjectReportData,
  type ReportSummary,
  type ProjectProgressChart,
  type StatusDistributionChart,
  type MonthlyCompletionChart,
  type PriorityDistributionChart,
  type DocumentTypeChart
} from "@/lib/services/reportService"

export default function ReportsPage() {
  const [selectedProject, setSelectedProject] = useState("all")
  const [reportType, setReportType] = useState("summary")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Data states
  const [projects, setProjects] = useState<ProjectReportData[]>([])
  const [summary, setSummary] = useState<ReportSummary | null>(null)
  const [projectProgressChart, setProjectProgressChart] = useState<ProjectProgressChart[]>([])
  const [statusDistribution, setStatusDistribution] = useState<StatusDistributionChart[]>([])
  const [monthlyCompletion, setMonthlyCompletion] = useState<MonthlyCompletionChart[]>([])
  const [priorityDistribution, setPriorityDistribution] = useState<PriorityDistributionChart[]>([])
  const [documentTypes, setDocumentTypes] = useState<DocumentTypeChart[]>([])

  const { toast } = useToast()

  useEffect(() => {
    loadReportData()
  }, [])

  const loadReportData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Load all report data in parallel
      const [
        projectReports,
        reportSummary,
        progressChart,
        statusChart,
        monthlyChart,
        priorityChart,
        docTypeChart
      ] = await Promise.all([
        ReportService.getProjectReports(),
        ReportService.getReportSummary(),
        ReportService.getProjectProgressChart(),
        ReportService.getStatusDistributionChart(),
        ReportService.getMonthlyCompletionChart(),
        ReportService.getPriorityDistributionChart(),
        ReportService.getDocumentTypeChart()
      ])

      setProjects(projectReports)
      setSummary(reportSummary)
      setProjectProgressChart(progressChart)
      setStatusDistribution(statusChart)
      setMonthlyCompletion(monthlyChart)
      setPriorityDistribution(priorityChart)
      setDocumentTypes(docTypeChart)
    } catch (error) {
      console.error('Error loading report data:', error)
      setError('Gagal memuat data laporan. Silakan coba lagi.')
      toast({
        title: "Error",
        description: "Gagal memuat data laporan. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const refreshData = () => {
    loadReportData()
  }

  const filteredData = selectedProject === "all" 
    ? projects 
    : projects.filter(project => project.id.toString() === selectedProject)

  const generatePDFReport = async () => {
    try {
      toast({
        title: "Export PDF",
        description: "Fitur export PDF akan segera tersedia.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal mengexport PDF. Silakan coba lagi.",
        variant: "destructive",
      })
    }
  }

  const generateExcelReport = async () => {
    try {
      const csvData = await ReportService.exportProjectData('csv')
      const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      
      if (link.download !== undefined) {
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `project-report-${new Date().toISOString().split('T')[0]}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }

      toast({
        title: "Export Berhasil",
        description: "Data laporan telah diexport ke CSV.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal mengexport data. Silakan coba lagi.",
        variant: "destructive",
      })
    }
  }

  const printReport = () => {
    window.print()
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Memuat laporan...</span>
        </div>
      </div>
    )
  }

  if (error || !summary) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || 'Gagal memuat data laporan'}{" "}
            <Button variant="link" onClick={refreshData} className="p-0 h-auto">
              Coba lagi
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <>
      <header className="flex h-16 shrink-0 items-center justify-between border-b px-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <h1 className="text-2xl font-bold">Laporan</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={refreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={printReport}>
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
          <Button variant="outline" size="sm" onClick={generateExcelReport}>
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button size="sm" onClick={generatePDFReport}>
            <Download className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </header>

      <div className="flex-1 p-6 space-y-6">
        {/* Report Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filter Laporan</CardTitle>
            <CardDescription>Pilih project dan jenis laporan yang ingin ditampilkan</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Project</label>
                <Select value={selectedProject} onValueChange={setSelectedProject}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih project" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Project</SelectItem>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id.toString()}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Jenis Laporan</label>
                <Select value={reportType} onValueChange={setReportType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Pilih jenis laporan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="summary">Ringkasan</SelectItem>
                    <SelectItem value="detailed">Detail</SelectItem>
                    <SelectItem value="analytics">Analytics</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Project</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalProjects}</div>
              <p className="text-xs text-muted-foreground">
                {summary.activeProjects} aktif, {summary.completedProjects} selesai
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Checklist</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalChecklists}</div>
              <p className="text-xs text-muted-foreground">
                {summary.completedChecklists} selesai
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Rata-rata Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.averageProgress}%</div>
              <Progress value={summary.averageProgress} className="mt-2" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Storage</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalDocuments}</div>
              <p className="text-xs text-muted-foreground">
                {summary.totalStorageSize} MB
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Report Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full lg:w-[600px] grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="projects">Projects</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Status Distribution Pie Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <PieChartIcon className="h-5 w-5" />
                    Distribusi Status Checklist
                  </CardTitle>
                  <CardDescription>Pembagian status semua checklist</CardDescription>
                </CardHeader>
                <CardContent>
                  {statusDistribution.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={statusDistribution}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={120}
                          paddingAngle={5}
                          dataKey="value"
                        >
                          {statusDistribution.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value, name, props) => [
                          `${value}% (${props.payload.count} item)`,
                          name
                        ]} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                      <div className="text-center">
                        <PieChartIcon className="h-12 w-12 mx-auto mb-2" />
                        <p>Belum ada data checklist</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Project Progress Bar Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Progress Project
                  </CardTitle>
                  <CardDescription>Status checklist per project</CardDescription>
                </CardHeader>
                <CardContent>
                  {projectProgressChart.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={projectProgressChart} layout="horizontal">
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" />
                        <YAxis dataKey="name" type="category" width={80} />
                        <Tooltip />
                        <Legend />
                        <Bar dataKey="completed" stackId="a" fill="#22c55e" name="Selesai" />
                        <Bar dataKey="inProgress" stackId="a" fill="#f59e0b" name="Progress" />
                        <Bar dataKey="pending" stackId="a" fill="#9ca3af" name="Pending" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                      <div className="text-center">
                        <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                        <p>Belum ada data project</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Monthly Completion Trend */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Tren Completion 12 Bulan
                  </CardTitle>
                  <CardDescription>Perbandingan checklist dibuat vs selesai</CardDescription>
                </CardHeader>
                <CardContent>
                  {monthlyCompletion.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <AreaChart data={monthlyCompletion}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Area 
                          type="monotone" 
                          dataKey="created" 
                          stackId="1" 
                          stroke="#3b82f6" 
                          fill="#3b82f6" 
                          name="Dibuat"
                          fillOpacity={0.6}
                        />
                        <Area 
                          type="monotone" 
                          dataKey="completed" 
                          stackId="2" 
                          stroke="#22c55e" 
                          fill="#22c55e" 
                          name="Selesai"
                          fillOpacity={0.8}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                      <div className="text-center">
                        <TrendingUp className="h-12 w-12 mx-auto mb-2" />
                        <p>Belum ada data bulanan</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Priority Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>Distribusi Prioritas</CardTitle>
                  <CardDescription>Pembagian checklist berdasarkan prioritas</CardDescription>
                </CardHeader>
                <CardContent>
                  {priorityDistribution.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={priorityDistribution}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="priority" />
                        <YAxis />
                        <Tooltip formatter={(value, name) => [`${value} item`, name]} />
                        <Bar dataKey="count" fill="#8884d8">
                          {priorityDistribution.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                      <div className="text-center">
                        <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                        <p>Belum ada data prioritas</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Document Types Distribution */}
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Distribusi Tipe Dokumen
                  </CardTitle>
                  <CardDescription>Jumlah dan ukuran file berdasarkan tipe</CardDescription>
                </CardHeader>
                <CardContent>
                  {documentTypes.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={documentTypes}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="type" />
                        <YAxis yAxisId="left" />
                        <YAxis yAxisId="right" orientation="right" />
                        <Tooltip formatter={(value, name) => [
                          name === 'count' ? `${value} file` : `${value} MB`,
                          name === 'count' ? 'Jumlah File' : 'Total Size'
                        ]} />
                        <Legend />
                        <Bar yAxisId="left" dataKey="count" fill="#3b82f6" name="Jumlah File" />
                        <Bar yAxisId="right" dataKey="size" fill="#22c55e" name="Size (MB)" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                      <div className="text-center">
                        <FileText className="h-12 w-12 mx-auto mb-2" />
                        <p>Belum ada dokumen</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Projects Tab */}
          <TabsContent value="projects" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Detail Project</CardTitle>
                <CardDescription>
                  Laporan lengkap untuk {selectedProject === "all" ? "semua project" : filteredData[0]?.name}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {filteredData.length > 0 ? (
                  <div className="space-y-6">
                    {filteredData.map((project) => (
                      <div key={project.id} className="border rounded-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div>
                            <h3 className="text-lg font-semibold">{project.name}</h3>
                            <p className="text-sm text-muted-foreground">
                              Dibuat: {ReportService.formatDate(project.created_at)}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={ReportService.getStatusBadgeVariant(project.status)}>
                              {project.status}
                            </Badge>
                            <div className="text-right">
                              <div className="text-sm font-medium">{project.progress}%</div>
                              <Progress value={project.progress} className="w-20" />
                            </div>
                          </div>
                        </div>

                        {project.description && (
                          <p className="text-sm text-muted-foreground mb-4">
                            {project.description}
                          </p>
                        )}

                        {/* Project Statistics */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                          <div className="text-center p-3 bg-muted rounded-lg">
                            <div className="text-lg font-bold">{project.totalChecklist}</div>
                            <div className="text-xs text-muted-foreground">Total Checklist</div>
                          </div>
                          <div className="text-center p-3 bg-green-50 rounded-lg">
                            <div className="text-lg font-bold text-green-600">{project.completedChecklist}</div>
                            <div className="text-xs text-muted-foreground">Selesai</div>
                          </div>
                          <div className="text-center p-3 bg-yellow-50 rounded-lg">
                            <div className="text-lg font-bold text-yellow-600">{project.inProgressChecklist}</div>
                            <div className="text-xs text-muted-foreground">Progress</div>
                          </div>
                          <div className="text-center p-3 bg-gray-50 rounded-lg">
                            <div className="text-lg font-bold text-gray-600">{project.pendingChecklist}</div>
                            <div className="text-xs text-muted-foreground">Pending</div>
                          </div>
                        </div>

                        {/* Indicators Breakdown */}
                        {project.indicators.length > 0 && (
                          <div className="mb-6">
                            <h4 className="font-medium mb-3">Progress per Indikator</h4>
                            <div className="space-y-3">
                              {project.indicators.map((indicator) => (
                                <div key={indicator.id} className="flex items-center justify-between p-3 border rounded">
                                  <div>
                                    <div className="font-medium">{indicator.name}</div>
                                    <div className="text-sm text-muted-foreground">
                                      {indicator.completedChecklist}/{indicator.totalChecklist} checklist
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <Progress value={indicator.progress} className="w-20" />
                                    <span className="text-sm font-medium">{indicator.progress}%</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Documents */}
                        {project.documents.length > 0 && (
                          <div>
                            <h4 className="font-medium mb-3">Dokumen ({project.documents.length})</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                              {project.documents.slice(0, 6).map((doc) => (
                                <div key={doc.id} className="flex items-center space-x-2 p-2 border rounded text-sm">
                                  <FileText className="h-4 w-4 text-muted-foreground" />
                                  <div className="flex-1 min-w-0">
                                    <div className="truncate">{doc.file_name}</div>
                                    <div className="text-xs text-muted-foreground">
                                      {ReportService.formatFileSize(doc.file_size)}
                                    </div>
                                  </div>
                                </div>
                              ))}
                              {project.documents.length > 6 && (
                                <div className="text-sm text-muted-foreground p-2">
                                  +{project.documents.length - 6} dokumen lainnya
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Tidak ada data project</h3>
                    <p className="text-muted-foreground">
                      Pilih project yang berbeda atau buat project baru
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  )
}
