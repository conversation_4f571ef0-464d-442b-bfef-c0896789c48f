'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { 
  Key, 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock,
  ExternalLink,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react'
import Link from 'next/link'

interface TokenInfo {
  hasToken: boolean
  hasRefreshToken: boolean
  isValid?: boolean
  expiresAt?: string
  scopes?: string[]
  error?: string
}

export default function GoogleTokensPage() {
  const [tokenInfo, setTokenInfo] = useState<TokenInfo>({ hasToken: false, hasRefreshToken: false })
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [showTokens, setShowTokens] = useState(false)
  const [newAccessToken, setNewAccessToken] = useState('')
  const [newRefreshToken, setNewRefreshToken] = useState('')
  const { toast } = useToast()

  useEffect(() => {
    checkTokenStatus()
  }, [])

  const checkTokenStatus = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/google-tokens/status')
      const data = await response.json()
      setTokenInfo(data)
    } catch (error) {
      console.error('Error checking token status:', error)
      toast({
        title: "Error",
        description: "Gagal mengecek status token",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const refreshToken = async () => {
    try {
      setIsRefreshing(true)
      const response = await fetch('/api/google-tokens/refresh', {
        method: 'POST'
      })
      
      const result = await response.json()
      
      if (result.success) {
        toast({
          title: "Token Refreshed",
          description: "Access token berhasil di-refresh"
        })
        await checkTokenStatus()
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('Error refreshing token:', error)
      toast({
        title: "Refresh Failed",
        description: error instanceof Error ? error.message : "Gagal refresh token",
        variant: "destructive"
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  const updateTokens = async () => {
    if (!newAccessToken.trim()) {
      toast({
        title: "Error",
        description: "Access token tidak boleh kosong",
        variant: "destructive"
      })
      return
    }

    try {
      const response = await fetch('/api/google-tokens/update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          accessToken: newAccessToken.trim(),
          refreshToken: newRefreshToken.trim() || undefined
        })
      })

      const result = await response.json()
      
      if (result.success) {
        toast({
          title: "Tokens Updated",
          description: "Google tokens berhasil diupdate"
        })
        setNewAccessToken('')
        setNewRefreshToken('')
        await checkTokenStatus()
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('Error updating tokens:', error)
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Gagal update tokens",
        variant: "destructive"
      })
    }
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied",
      description: `${label} copied to clipboard`
    })
  }

  const getStatusBadge = () => {
    if (isLoading) return <Badge variant="secondary">Checking...</Badge>
    if (!tokenInfo.hasToken) return <Badge variant="destructive">No Token</Badge>
    if (tokenInfo.error) return <Badge variant="destructive">Invalid</Badge>
    if (tokenInfo.isValid) return <Badge variant="default">Valid</Badge>
    return <Badge variant="secondary">Unknown</Badge>
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Key className="h-8 w-8" />
            Google Token Management
          </h1>
          <p className="text-muted-foreground mt-2">
            Kelola Google OAuth tokens untuk integrasi Google Sheets
          </p>
        </div>
        <Button variant="outline" onClick={checkTokenStatus} disabled={isLoading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Refresh Status
        </Button>
      </div>

      {/* Current Token Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Current Token Status
            {getStatusBadge()}
          </CardTitle>
          <CardDescription>
            Status dan informasi Google OAuth tokens yang sedang digunakan
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              {tokenInfo.hasToken ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              <span>Access Token: {tokenInfo.hasToken ? 'Available' : 'Missing'}</span>
            </div>
            
            <div className="flex items-center gap-2">
              {tokenInfo.hasRefreshToken ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-orange-500" />
              )}
              <span>Refresh Token: {tokenInfo.hasRefreshToken ? 'Available' : 'Missing'}</span>
            </div>
          </div>

          {tokenInfo.expiresAt && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>Expires: {new Date(tokenInfo.expiresAt).toLocaleString('id-ID')}</span>
            </div>
          )}

          {tokenInfo.scopes && tokenInfo.scopes.length > 0 && (
            <div>
              <Label className="text-sm font-medium">Granted Scopes:</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {tokenInfo.scopes.map((scope, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {scope.replace('https://www.googleapis.com/auth/', '')}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {tokenInfo.error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{tokenInfo.error}</AlertDescription>
            </Alert>
          )}

          {tokenInfo.hasRefreshToken && (
            <div className="pt-2">
              <Button 
                onClick={refreshToken} 
                disabled={isRefreshing}
                variant="outline"
              >
                {isRefreshing ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Refresh Access Token
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Update Tokens */}
      <Card>
        <CardHeader>
          <CardTitle>Update Tokens</CardTitle>
          <CardDescription>
            Update Google OAuth tokens secara manual. Gunakan{' '}
            <Button variant="link" className="p-0 h-auto" asChild>
              <Link href="/scripts/get-google-oauth-token.html" target="_blank">
                OAuth Token Generator <ExternalLink className="h-3 w-3 ml-1" />
              </Link>
            </Button>
            {' '}untuk mendapatkan token baru.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="accessToken">Access Token *</Label>
            <div className="flex gap-2 mt-1">
              <Input
                id="accessToken"
                type={showTokens ? "text" : "password"}
                value={newAccessToken}
                onChange={(e) => setNewAccessToken(e.target.value)}
                placeholder="ya29.a0AfH6SMC..."
                className="font-mono text-sm"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={() => setShowTokens(!showTokens)}
              >
                {showTokens ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <div>
            <Label htmlFor="refreshToken">Refresh Token (Optional)</Label>
            <div className="flex gap-2 mt-1">
              <Input
                id="refreshToken"
                type={showTokens ? "text" : "password"}
                value={newRefreshToken}
                onChange={(e) => setNewRefreshToken(e.target.value)}
                placeholder="1//04..."
                className="font-mono text-sm"
              />
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Refresh token diperlukan untuk auto-refresh access token
            </p>
          </div>

          <Button onClick={updateTokens} disabled={!newAccessToken.trim()}>
            Update Tokens
          </Button>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Aksi cepat untuk mengelola Google Sheets integration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" asChild>
              <Link href="/scripts/get-google-oauth-token.html" target="_blank">
                <Key className="h-4 w-4 mr-2" />
                Generate New Token
              </Link>
            </Button>
            
            <Button variant="outline" asChild>
              <Link href="https://console.cloud.google.com/apis/credentials" target="_blank">
                <ExternalLink className="h-4 w-4 mr-2" />
                Google Cloud Console
              </Link>
            </Button>
            
            <Button variant="outline" asChild>
              <Link href="https://myaccount.google.com/permissions" target="_blank">
                <ExternalLink className="h-4 w-4 mr-2" />
                Google Account Permissions
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}