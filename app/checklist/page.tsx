"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, Clock, Search, FolderOpen, MoreHorizontal, Loader2, AlertCircle, ExternalLink } from "lucide-react"
import Link from "next/link"
import { useToast } from "@/hooks/use-toast"
import { ChecklistService, type ChecklistItemWithDetails, type ChecklistStats, type ChecklistFilters } from "@/lib/services/checklistService"
import { ProjectService } from "@/lib/services/projectService"
import type { ChecklistStatus, Project } from "@/lib/supabase"

export default function ChecklistPage() {
  const [checklists, setChecklists] = useState<ChecklistItemWithDetails[]>([])
  const [projects, setProjects] = useState<Project[]>([])
  const [stats, setStats] = useState<ChecklistStats>({ total: 0, completed: 0, in_progress: 0, pending: 0 })
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Filter states
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<"all" | "completed" | "in_progress" | "pending">("all")
  const [priorityFilter, setPriorityFilter] = useState<"all" | "high" | "medium" | "low">("all")
  const [projectFilter, setProjectFilter] = useState<"all" | string>("all")
  
  const { toast } = useToast()

  useEffect(() => {
    loadInitialData()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [searchTerm, statusFilter, priorityFilter, projectFilter])

  const loadInitialData = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      // Load projects and stats in parallel
      const [projectsData, statsData] = await Promise.all([
        ProjectService.getAllProjects(),
        ChecklistService.getChecklistStats()
      ])

      setProjects(projectsData)
      setStats(statsData)

      // Load all checklists initially
      await applyFilters()
    } catch (error) {
      console.error('Error loading initial data:', error)
      setError('Gagal memuat data checklist. Silakan coba lagi.')
      toast({
        title: "Error",
        description: "Gagal memuat data checklist. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const applyFilters = async () => {
    try {
      const filters: ChecklistFilters = {
        searchTerm: searchTerm || undefined,
        statusFilter: statusFilter === 'all' ? undefined : statusFilter,
        priorityFilter: priorityFilter === 'all' ? undefined : priorityFilter,
        projectFilter: projectFilter === 'all' ? undefined : projectFilter,
      }

      const filteredData = await ChecklistService.getFilteredChecklistItems(filters)
      setChecklists(filteredData)
    } catch (error) {
      console.error('Error applying filters:', error)
      toast({
        title: "Error",
        description: "Gagal memfilter data checklist.",
        variant: "destructive",
      })
    }
  }

  const toggleChecklistStatus = async (checklistId: string) => {
    try {
      await ChecklistService.toggleChecklistStatus(checklistId)
      
      // Refresh data
      await Promise.all([
        applyFilters(),
        refreshStats()
      ])

      toast({
        title: "Berhasil",
        description: "Status checklist berhasil diperbarui.",
      })
    } catch (error) {
      console.error('Error toggling checklist status:', error)
      toast({
        title: "Error",
        description: "Gagal memperbarui status checklist.",
        variant: "destructive",
      })
    }
  }

  const updateChecklistStatus = async (checklistId: string, status: ChecklistStatus) => {
    try {
      await ChecklistService.updateChecklistStatus(checklistId, status)
      
      // Update local state
      setChecklists(prevChecklists =>
        prevChecklists.map(item =>
          item.id === checklistId
            ? { 
                ...item, 
                status_label: status,
                status: status === 'completed',
                completed_at: status === 'completed' ? new Date().toISOString() : null
              }
            : item
        )
      )

      // Refresh stats
      await refreshStats()

      toast({
        title: "Berhasil",
        description: "Status checklist berhasil diperbarui.",
      })
    } catch (error) {
      console.error('Error updating checklist status:', error)
      toast({
        title: "Error",
        description: "Gagal memperbarui status checklist.",
        variant: "destructive",
      })
    }
  }

  const refreshStats = async () => {
    try {
      const newStats = await ChecklistService.getChecklistStats()
      setStats(newStats)
    } catch (error) {
      console.error('Error refreshing stats:', error)
    }
  }

  const clearFilters = () => {
    setSearchTerm("")
    setStatusFilter("all")
    setPriorityFilter("all")
    setProjectFilter("all")
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusBadge = (statusLabel: string | null, status: boolean) => {
    const label = ChecklistService.getStatusLabel(statusLabel, status)
    const color = ChecklistService.getStatusColor(statusLabel, status)
    
    return <Badge variant="default" className={color}>{label}</Badge>
  }

  const getPriorityBadge = (priority: string) => {
    const label = ChecklistService.getPriorityLabel(priority)
    const color = ChecklistService.getPriorityColor(priority)
    
    return <Badge variant="outline" className={color}>{label}</Badge>
  }

  const getStatusIcon = (statusLabel: string | null, status: boolean) => {
    const currentStatus = statusLabel || (status ? 'completed' : 'pending')
    
    switch (currentStatus) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'in_progress':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'pending':
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Memuat data checklist...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}{" "}
            <Button variant="link" onClick={loadInitialData} className="p-0 h-auto">
              Coba lagi
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
        <SidebarTrigger className="-ml-1" />
        <h1 className="text-2xl font-bold">Checklist Project</h1>
      </header>

      <div className="flex-1 p-6 space-y-6">
        {/* Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Checklist</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Selesai</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Progress</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{stats.in_progress}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-600">{stats.pending}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Filter & Pencarian</CardTitle>
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Reset Filter
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Cari checklist, project, indikator..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Select value={statusFilter} onValueChange={(value: typeof statusFilter) => setStatusFilter(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="completed">Selesai</SelectItem>
                  <SelectItem value="in_progress">Progress</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
              <Select value={priorityFilter} onValueChange={(value: typeof priorityFilter) => setPriorityFilter(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Prioritas" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Prioritas</SelectItem>
                  <SelectItem value="high">Tinggi</SelectItem>
                  <SelectItem value="medium">Sedang</SelectItem>
                  <SelectItem value="low">Rendah</SelectItem>
                </SelectContent>
              </Select>
              <Select value={projectFilter} onValueChange={setProjectFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Project" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Project</SelectItem>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Checklist Items */}
        <div className="space-y-4">
          {checklists.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Tidak ada checklist</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all' || projectFilter !== 'all'
                    ? 'Tidak ada checklist yang sesuai dengan filter'
                    : 'Belum ada checklist item yang dibuat'
                  }
                </p>
                {(searchTerm || statusFilter !== 'all' || priorityFilter !== 'all' || projectFilter !== 'all') && (
                  <Button variant="outline" onClick={clearFilters}>
                    Reset Filter
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            checklists.map((checklist) => (
              <Card key={checklist.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={checklist.status}
                      onCheckedChange={() => toggleChecklistStatus(checklist.id)}
                    />
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(checklist.status_label, checklist.status)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <p className={`font-medium ${checklist.status ? "line-through text-muted-foreground" : ""}`}>
                            {checklist.description}
                          </p>
                          <div className="flex items-center space-x-2 mt-1 text-sm text-muted-foreground">
                            <span className="font-medium">{checklist.project_name}</span>
                            <span>•</span>
                            <span>{checklist.indicator_name}</span>
                            {checklist.completed_at && (
                              <>
                                <span>•</span>
                                <span>Selesai: {formatDate(checklist.completed_at)}</span>
                              </>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          {getStatusBadge(checklist.status_label, checklist.status)}
                          {getPriorityBadge(checklist.priority)}
                          <Link href={`/projects/${checklist.project_id}`}>
                            <Button variant="ghost" size="sm">
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          </Link>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => updateChecklistStatus(checklist.id, 'pending')}>
                                <Clock className="h-4 w-4 mr-2" />
                                Set Pending
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => updateChecklistStatus(checklist.id, 'in_progress')}>
                                <Clock className="h-4 w-4 mr-2 text-yellow-500" />
                                Set In Progress
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => updateChecklistStatus(checklist.id, 'completed')}>
                                <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                                Set Completed
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Show filter summary if filters are applied */}
        {(searchTerm || statusFilter !== 'all' || priorityFilter !== 'all' || projectFilter !== 'all') && (
          <div className="text-sm text-muted-foreground text-center py-2">
            Menampilkan {checklists.length} dari {stats.total} checklist total
          </div>
        )}
      </div>
    </>
  )
}
