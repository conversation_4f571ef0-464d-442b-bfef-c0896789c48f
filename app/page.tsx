"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, FolderOpen, FileText, TrendingUp, Activity, HardDrive, Loader2, AlertCircle, Plus, BarChart3 } from "lucide-react"
import Link from "next/link"
import { useToast } from "@/hooks/use-toast"
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from "recharts"
import { 
  DashboardService, 
  type DashboardStats, 
  type ProjectStats, 
  type StatusDistribution, 
  type MonthlyProgress,
  type RecentActivity 
} from "@/lib/services/dashboardService"

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [projects, setProjects] = useState<ProjectStats[]>([])
  const [statusDistribution, setStatusDistribution] = useState<StatusDistribution[]>([])
  const [monthlyProgress, setMonthlyProgress] = useState<MonthlyProgress[]>([])
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const { toast } = useToast()

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Load all dashboard data in parallel
      const [
        dashboardStats,
        projectsData,
        statusData,
        monthlyData,
        activitiesData
      ] = await Promise.all([
        DashboardService.getDashboardStats(),
        DashboardService.getProjectsWithProgress(),
        DashboardService.getStatusDistribution(),
        DashboardService.getMonthlyProgress(),
        DashboardService.getRecentActivities()
      ])

      setStats(dashboardStats)
      setProjects(projectsData)
      setStatusDistribution(statusData)
      setMonthlyProgress(monthlyData)
      setRecentActivities(activitiesData)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
      setError('Gagal memuat data dashboard. Silakan coba lagi.')
      toast({
        title: "Error",
        description: "Gagal memuat data dashboard. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const refreshData = () => {
    loadDashboardData()
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Memuat dashboard...</span>
        </div>
      </div>
    )
  }

  if (error || !stats) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || 'Gagal memuat data dashboard'}{" "}
            <Button variant="link" onClick={refreshData} className="p-0 h-auto">
              Coba lagi
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <>
      <header className="flex h-16 shrink-0 items-center justify-between border-b px-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <h1 className="text-2xl font-bold">Dashboard</h1>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={refreshData}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button asChild size="sm">
            <Link href="/projects">
              <Plus className="h-4 w-4 mr-2" />
              Buat Project
            </Link>
          </Button>
        </div>
      </header>

      <div className="flex-1 p-6 space-y-6">
        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Project</CardTitle>
              <FolderOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalProjects}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeProjects} aktif
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Checklist Selesai</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completedChecklists}</div>
              <p className="text-xs text-muted-foreground">
                dari {stats.totalChecklists} total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Progress Keseluruhan</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.overallProgress}%</div>
              <Progress value={stats.overallProgress} className="mt-2" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Storage</CardTitle>
              <HardDrive className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalDocuments}</div>
              <p className="text-xs text-muted-foreground">
                {stats.totalStorageSize} MB terpakai
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Status Checklist</CardTitle>
              <CardDescription>Distribusi status semua checklist</CardDescription>
            </CardHeader>
            <CardContent>
              {statusDistribution.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={statusDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {statusDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value}%`, 'Persentase']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                    <p>Belum ada data checklist</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Progress 6 Bulan Terakhir</CardTitle>
              <CardDescription>Perkembangan checklist per bulan</CardDescription>
            </CardHeader>
            <CardContent>
              {monthlyProgress.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={monthlyProgress}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="completed" fill="#22c55e" name="Selesai" />
                    <Bar dataKey="in_progress" fill="#f59e0b" name="Progress" />
                    <Bar dataKey="pending" fill="#9ca3af" name="Pending" />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2" />
                    <p>Belum ada data bulanan</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Projects and Activities */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Projects */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Project Terbaru</CardTitle>
                  <CardDescription>Daftar project dengan status terkini</CardDescription>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/projects">Lihat Semua</Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {projects.length > 0 ? (
                <div className="space-y-4">
                  {projects.slice(0, 5).map((project) => (
                    <div key={project.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <FolderOpen className="h-8 w-8 text-primary" />
                        </div>
                        <div className="min-w-0">
                          <h3 className="font-medium truncate">{project.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {project.completedChecklist}/{project.totalChecklist} checklist selesai
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="text-right">
                          <div className="text-sm font-medium">{project.progress}%</div>
                          <Progress value={project.progress} className="w-16" />
                        </div>
                        <Badge variant={project.status === "active" ? "default" : "secondary"}>
                          {project.status === "active" ? "Aktif" : "Pending"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Belum ada project</h3>
                  <p className="text-muted-foreground mb-4">
                    Mulai dengan membuat project pertama
                  </p>
                  <Button asChild>
                    <Link href="/projects">
                      <Plus className="h-4 w-4 mr-2" />
                      Buat Project
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Aktivitas Terbaru</CardTitle>
                  <CardDescription>Perubahan dan aktivitas terkini</CardDescription>
                </div>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              {recentActivities.length > 0 ? (
                <div className="space-y-4">
                  {recentActivities.slice(0, 8).map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex-shrink-0 text-lg">
                        {DashboardService.getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium">{activity.title}</p>
                          <span className="text-xs text-muted-foreground">
                            {DashboardService.formatDate(activity.timestamp)}
                          </span>
                        </div>
                        <p className="text-sm text-muted-foreground truncate">
                          {activity.description}
                        </p>
                        {activity.projectName && (
                          <p className="text-xs text-muted-foreground mt-1">
                            Project: {activity.projectName}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Belum ada aktivitas</h3>
                  <p className="text-muted-foreground">
                    Aktivitas akan muncul setelah ada checklist atau dokumen
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Akses cepat ke fitur utama</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Button variant="outline" asChild className="h-20 flex-col space-y-2">
                <Link href="/projects">
                  <FolderOpen className="h-6 w-6" />
                  <span>Kelola Project</span>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-20 flex-col space-y-2">
                <Link href="/checklist">
                  <CheckCircle className="h-6 w-6" />
                  <span>Lihat Checklist</span>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-20 flex-col space-y-2">
                <Link href="/documents">
                  <FileText className="h-6 w-6" />
                  <span>Kelola Dokumen</span>
                </Link>
              </Button>
              <Button variant="outline" asChild className="h-20 flex-col space-y-2">
                <Link href="/reports">
                  <TrendingUp className="h-6 w-6" />
                  <span>Lihat Laporan</span>
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
