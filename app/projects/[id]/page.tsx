"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Checkbox } from "@/components/ui/checkbox"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { ArrowLeft, CheckCircle, Clock, FileText, Plus, Upload, Loader2, AlertCircle, MoreHorizontal, Trash2, Edit, ExternalLink, RefreshCw } from "lucide-react"
import Link from "next/link"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { ChecklistItemService } from "@/lib/services/indicatorService"
import type { Project, ChecklistStatus } from "@/lib/supabase"

interface ProjectDetailPageProps {
  params: Promise<{ id: string }>
}

// Tambahkan ulang tipe data yang hilang
interface IndicatorWithChecklist {
  id: string
  name: string
  description: string
  checklist_items: any[]
}

interface Project {
  id: string
  name: string
  description?: string
  status: string
  created_at: string
  google_sheet_url?: string
}

export default function ProjectDetailPage({ params }: ProjectDetailPageProps) {
  const [projectId, setProjectId] = useState<string>("")
  const [project, setProject] = useState<Project | null>(null)
  const [indicators, setIndicators] = useState<IndicatorWithChecklist[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isAddingIndicator, setIsAddingIndicator] = useState(false)
  const [isAddingChecklist, setIsAddingChecklist] = useState(false)
  const [activeIndicatorId, setActiveIndicatorId] = useState<string>("")
  const [newIndicator, setNewIndicator] = useState({ name: "", description: "" })
  const [newChecklist, setNewChecklist] = useState({ 
    description: "", 
    priority: "medium" as "high" | "medium" | "low" 
  })
  const [googleSheetUrl, setGoogleSheetUrl] = useState<string | null>(null)
  const [isSyncingSheet, setIsSyncingSheet] = useState(false)
  const { toast } = useToast()

  // Resolve params and set project ID
  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params
      setProjectId(resolvedParams.id)
    }
    resolveParams()
  }, [params])

  // Load project data when projectId is set
  useEffect(() => {
    if (projectId) {
      loadProjectData()
    }
  }, [projectId])

  const loadProjectData = async () => {
    if (!projectId) return
    
    try {
      setIsLoading(true)
      setError(null)
      
      // Refactor: fetch data project dan indikator dari API route
      const [projectRes, indicatorsRes] = await Promise.all([
        fetch(`/api/projects/${projectId}`),
        fetch(`/api/projects/${projectId}/indicators`)
      ])
      if (!projectRes.ok) throw new Error('Gagal fetch data project')
      if (!indicatorsRes.ok) throw new Error('Gagal fetch data indikator')
      const projectData = await projectRes.json()
      const indicatorsData = await indicatorsRes.json()

      if (!projectData) {
        setError('Project tidak ditemukan')
        return
      }

      setProject(projectData)
      setIndicators(indicatorsData)
      
      // Load Google Sheet URL if available
      setGoogleSheetUrl(projectData.google_sheet_url || null)
    } catch (error) {
      console.error('Error loading project data:', error)
      setError('Gagal memuat data project. Silakan coba lagi.')
      toast({
        title: "Error",
        description: "Gagal memuat data project. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const toggleChecklistStatus = async (checklistId: string) => {
    try {
      await ChecklistItemService.toggleChecklistStatus(checklistId)
      
      // Reload data to get updated status
      await loadProjectData()

      toast({
        title: "Berhasil",
        description: "Status checklist berhasil diperbarui.",
      })
    } catch (error) {
      console.error('Error toggling checklist status:', error)
      toast({
        title: "Error",
        description: "Gagal memperbarui status checklist.",
        variant: "destructive",
      })
    }
  }

  const updateChecklistStatus = async (checklistId: string, status: ChecklistStatus) => {
    try {
      await ChecklistItemService.updateChecklistStatus(checklistId, status)
      
      // Update local state
      setIndicators(prevIndicators =>
        prevIndicators.map(indicator => ({
          ...indicator,
          checklist_items: indicator.checklist_items.map(item =>
            item.id === checklistId
              ? { 
                  ...item, 
                  status_label: status,
                  status: status === 'completed',
                  completed_at: status === 'completed' ? new Date().toISOString() : null
                }
              : item
          )
        }))
      )

      toast({
        title: "Berhasil",
        description: "Status checklist berhasil diperbarui.",
      })
    } catch (error) {
      console.error('Error updating checklist status:', error)
      toast({
        title: "Error",
        description: "Gagal memperbarui status checklist.",
        variant: "destructive",
      })
    }
  }

  const deleteChecklistItem = async (checklistId: string, description: string) => {
    if (!confirm(`Apakah Anda yakin ingin menghapus checklist "${description}"?`)) {
      return
    }

    try {
      await ChecklistItemService.deleteChecklistItem(checklistId)
      
      // Update local state
      setIndicators(prevIndicators =>
        prevIndicators.map(indicator => ({
          ...indicator,
          checklist_items: indicator.checklist_items.filter(item => item.id !== checklistId)
        }))
      )

      toast({
        title: "Berhasil",
        description: "Checklist item berhasil dihapus.",
      })
    } catch (error) {
      console.error('Error deleting checklist item:', error)
      toast({
        title: "Error",
        description: "Gagal menghapus checklist item.",
        variant: "destructive",
      })
    }
  }

  const handleAddIndicator = async () => {
    if (!newIndicator.name) {
      toast({
        title: "Error",
        description: "Nama indikator wajib diisi.",
        variant: "destructive",
      })
      return
    }

    if (!projectId) return

    try {
      setIsAddingIndicator(true)
      const indicatorData = {
        project_id: projectId,
        name: newIndicator.name,
        description: newIndicator.description || null
      }

      const createdIndicator = await ChecklistItemService.createIndicator(indicatorData)
      
      // Add to local state with empty checklist_items
      setIndicators(prev => [...prev, { ...createdIndicator, checklist_items: [] }])
      
      setNewIndicator({ name: "", description: "" })
      setIsAddingIndicator(false)

      toast({
        title: "Berhasil",
        description: "Indikator berhasil ditambahkan.",
      })
    } catch (error) {
      console.error('Error adding indicator:', error)
      toast({
        title: "Error",
        description: "Gagal menambahkan indikator.",
        variant: "destructive",
      })
      setIsAddingIndicator(false)
    }
  }

  const handleAddChecklist = async () => {
    if (!newChecklist.description) {
      toast({
        title: "Error",
        description: "Deskripsi checklist wajib diisi.",
        variant: "destructive",
      })
      return
    }

    if (!activeIndicatorId) return

    try {
      setIsAddingChecklist(true)
      const checklistData = {
        indicator_id: activeIndicatorId,
        description: newChecklist.description,
        priority: newChecklist.priority,
        status: false
      }

      const createdChecklist = await ChecklistItemService.createChecklistItem(checklistData)
      
      // Add to local state
      setIndicators(prev => 
        prev.map(indicator => 
          indicator.id === activeIndicatorId
            ? { ...indicator, checklist_items: [...indicator.checklist_items, createdChecklist] }
            : indicator
        )
      )
      
      setNewChecklist({ description: "", priority: "medium" })
      setActiveIndicatorId("")
      setIsAddingChecklist(false)

      toast({
        title: "Berhasil",
        description: "Checklist item berhasil ditambahkan.",
      })
    } catch (error) {
      console.error('Error adding checklist item:', error)
      toast({
        title: "Error",
        description: "Gagal menambahkan checklist item.",
        variant: "destructive",
      })
      setIsAddingChecklist(false)
    }
  }

  const openAddChecklistDialog = (indicatorId: string) => {
    setActiveIndicatorId(indicatorId)
    setNewChecklist({ description: "", priority: "medium" })
  }

  const syncWithGoogleSheets = async () => {
    // Validasi projectId dan googleSheetUrl sebelum request
    if (!projectId || projectId.trim() === '') {
      console.log('syncWithGoogleSheets: projectId kosong:', projectId)
      toast({
        title: "Error",
        description: "Project ID tidak valid. Silakan refresh halaman.",
        variant: "destructive",
      })
      return
    }
    
    if (!googleSheetUrl) {
      toast({
        title: "Error",
        description: "Google Sheet URL tidak tersedia.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSyncingSheet(true)
      
      // Extract sheet ID from URL
      const sheetIdMatch = googleSheetUrl.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/)
      if (!sheetIdMatch) {
        throw new Error('Invalid Google Sheet URL')
      }
      
      const sheetId = sheetIdMatch[1]
      
      // Tambahkan log untuk debug
      console.log('syncWithGoogleSheets: sending projectId:', projectId, 'sheetId:', sheetId)
      
      const response = await fetch('/api/sync-google-sheets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId: projectId, // HAPUS parseInt(), kirim UUID langsung
          sheetId: sheetId,
          action: 'sync-checklist'
        })
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to sync with Google Sheets')
      }

      // Reload project data to show updated status
      await loadProjectData()

      toast({
        title: "Sinkronisasi Berhasil",
        description: `${result.data?.successCount || 0} checklist berhasil disinkronkan dari Google Sheets.`,
      })
    } catch (error) {
      console.error('Error syncing with Google Sheets:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Gagal sinkronisasi dengan Google Sheets.",
        variant: "destructive",
      })
    } finally {
      setIsSyncingSheet(false)
    }
  }

  const createGoogleSheet = async () => {
    // Validasi projectId sebelum request
    if (!projectId || projectId.trim() === '') {
      console.log('createGoogleSheet: projectId kosong:', projectId)
      toast({
        title: "Error",
        description: "Project ID tidak valid. Silakan refresh halaman.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSyncingSheet(true)
      
      // Tambahkan log untuk debug
      console.log('createGoogleSheet: sending projectId:', projectId)
      
      const response = await fetch('/api/sync-google-sheets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectId: projectId, // HAPUS parseInt(), kirim UUID langsung
          action: 'create-sheet'
        })
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to create Google Sheet')
      }

      setGoogleSheetUrl(result.sheetUrl)

      toast({
        title: "Google Sheet Dibuat",
        description: "Google Sheet untuk project ini berhasil dibuat.",
      })
    } catch (error) {
      console.error('Error creating Google Sheet:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Gagal membuat Google Sheet.",
        variant: "destructive",
      })
    } finally {
      setIsSyncingSheet(false)
    }
  }

  const calculateProgress = () => {
    const totalChecklists = indicators.reduce((sum, indicator) => sum + indicator.checklist_items.length, 0)
    const completedChecklists = indicators.reduce(
      (sum, indicator) => sum + indicator.checklist_items.filter((item: any) => item.status).length,
      0,
    )
    return totalChecklists > 0 ? Math.round((completedChecklists / totalChecklists) * 100) : 0
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusBadge = (statusLabel: string | null, status: boolean) => {
    const currentStatus = statusLabel || (status ? 'completed' : 'pending')
    
    switch (currentStatus) {
      case 'completed':
        return <Badge variant="default" className="bg-green-500">Selesai</Badge>
      case 'in_progress':
        return <Badge variant="default" className="bg-yellow-500">Progress</Badge>
      case 'pending':
      default:
        return <Badge variant="secondary">Pending</Badge>
    }
  }

  const getStatusIcon = (statusLabel: string | null, status: boolean) => {
    const currentStatus = statusLabel || (status ? 'completed' : 'pending')
    
    switch (currentStatus) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'in_progress':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'pending':
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Memuat data project...</span>
        </div>
      </div>
    )
  }

  if (error || !project) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error || 'Project tidak ditemukan'}{" "}
            <Button variant="link" onClick={loadProjectData} className="p-0 h-auto">
              Coba lagi
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-4 border-b px-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/projects">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali
          </Link>
        </Button>
        <SidebarTrigger className="-ml-1" />
        <div className="flex-1">
          <h1 className="text-2xl font-bold">{project.name}</h1>
          <p className="text-muted-foreground">{project.description}</p>
        </div>
        <div className="flex items-center gap-2">
          {googleSheetUrl ? (
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={googleSheetUrl} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Buka Google Sheet
                </Link>
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={syncWithGoogleSheets}
                disabled={isSyncingSheet}
              >
                {isSyncingSheet ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Sinkronisasi
              </Button>
            </div>
          ) : (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={createGoogleSheet}
              disabled={isSyncingSheet}
            >
              {isSyncingSheet ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Plus className="h-4 w-4 mr-2" />
              )}
              Buat Google Sheet
            </Button>
          )}
          <Badge variant={project.status === "active" ? "default" : "secondary"}>
            {project.status === "active" ? "Aktif" : "Pending"}
          </Badge>
        </div>
      </header>

      <div className="flex-1 p-6">
        <Tabs defaultValue="checklist" className="space-y-6">
          <TabsList>
            <TabsTrigger value="checklist">Checklist</TabsTrigger>
            <TabsTrigger value="documents">Dokumentasi</TabsTrigger>
            <TabsTrigger value="overview">Overview</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Progress Keseluruhan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">{calculateProgress()}%</div>
                  <Progress value={calculateProgress()} className="mb-2" />
                  <p className="text-sm text-muted-foreground">
                    {indicators.reduce(
                      (sum, indicator) => sum + indicator.checklist_items.filter((item: any) => item.status).length,
                      0,
                    )}{" "}
                    dari {indicators.reduce((sum, indicator) => sum + indicator.checklist_items.length, 0)} checklist selesai
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Total Indikator</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">{indicators.length}</div>
                  <p className="text-sm text-muted-foreground">Kategori checklist</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Dokumen</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold mb-2">0</div>
                  <p className="text-sm text-muted-foreground">File terupload</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Informasi Project</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <Label className="text-sm font-medium">Nama Project</Label>
                    <p className="text-sm text-muted-foreground">{project.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Status</Label>
                    <p className="text-sm text-muted-foreground">
                      {project.status === "active" ? "Aktif" : "Pending"}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Tanggal Dibuat</Label>
                    <p className="text-sm text-muted-foreground">{formatDate(project.created_at)}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Penanggung Jawab</Label>
                    <p className="text-sm text-muted-foreground">{project.assignee || '-'}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Google Sheet</Label>
                    {googleSheetUrl ? (
                      <Link 
                        href={googleSheetUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
                      >
                        Buka Google Sheet <ExternalLink className="h-3 w-3" />
                      </Link>
                    ) : (
                      <p className="text-sm text-muted-foreground">Belum tersedia</p>
                    )}
                  </div>
                </div>
                {project.description && (
                  <div>
                    <Label className="text-sm font-medium">Deskripsi</Label>
                    <p className="text-sm text-muted-foreground">{project.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="checklist" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Checklist Indikator</h2>
                <p className="text-muted-foreground">Kelola checklist untuk setiap indikator project</p>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Tambah Indikator
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Tambah Indikator Baru</DialogTitle>
                    <DialogDescription>Buat indikator baru untuk mengorganisir checklist</DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="indicator-name">Nama Indikator *</Label>
                      <Input
                        id="indicator-name"
                        value={newIndicator.name}
                        onChange={(e) => setNewIndicator({ ...newIndicator, name: e.target.value })}
                        placeholder="Masukkan nama indikator"
                      />
                    </div>
                    <div className="grid gap-2">
                      <Label htmlFor="indicator-description">Deskripsi</Label>
                      <Textarea
                        id="indicator-description"
                        value={newIndicator.description}
                        onChange={(e) => setNewIndicator({ ...newIndicator, description: e.target.value })}
                        placeholder="Deskripsi indikator (opsional)"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button 
                      variant="outline" 
                      onClick={() => setNewIndicator({ name: "", description: "" })}
                      disabled={isAddingIndicator}
                    >
                      Batal
                    </Button>
                    <Button onClick={handleAddIndicator} disabled={isAddingIndicator}>
                      {isAddingIndicator && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                      Tambah Indikator
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            {/* Dialog for adding checklist item */}
            <Dialog open={!!activeIndicatorId} onOpenChange={(open) => !open && setActiveIndicatorId("")}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Tambah Checklist Item</DialogTitle>
                  <DialogDescription>
                    Tambahkan checklist item baru untuk indikator: {indicators.find(i => i.id === activeIndicatorId)?.name}
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid gap-2">
                    <Label htmlFor="checklist-description">Deskripsi Checklist *</Label>
                    <Textarea
                      id="checklist-description"
                      value={newChecklist.description}
                      onChange={(e) => setNewChecklist({ ...newChecklist, description: e.target.value })}
                      placeholder="Masukkan deskripsi checklist"
                      rows={3}
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="checklist-priority">Prioritas</Label>
                    <Select
                      value={newChecklist.priority}
                      onValueChange={(value: "high" | "medium" | "low") => 
                        setNewChecklist({ ...newChecklist, priority: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih prioritas" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button 
                    variant="outline" 
                    onClick={() => setActiveIndicatorId("")}
                    disabled={isAddingChecklist}
                  >
                    Batal
                  </Button>
                  <Button onClick={handleAddChecklist} disabled={isAddingChecklist}>
                    {isAddingChecklist && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    Tambah Checklist
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <div className="space-y-6">
              {indicators.map((indicator) => (
                <Card key={indicator.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{indicator.name}</CardTitle>
                        {indicator.description && (
                          <p className="text-sm text-muted-foreground mt-1">{indicator.description}</p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">
                          {indicator.checklist_items.filter(item => item.status).length} / {indicator.checklist_items.length}
                        </Badge>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => openAddChecklistDialog(indicator.id)}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Tambah Item
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {indicator.checklist_items.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-sm text-muted-foreground mb-3">
                          Belum ada checklist item untuk indikator ini
                        </p>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => openAddChecklistDialog(indicator.id)}
                        >
                          <Plus className="h-4 w-4 mr-1" />
                          Tambah Item Pertama
                        </Button>
                      </div>
                    ) : (
                      indicator.checklist_items.map((item: any) => (
                        <div key={item.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                          <Checkbox
                            checked={item.status}
                            onCheckedChange={() => toggleChecklistStatus(item.id)}
                          />
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(item.status_label, item.status)}
                          </div>
                          <div className="flex-1">
                            <p className={`text-sm ${item.status ? "line-through text-muted-foreground" : ""}`}>
                              {item.description}
                            </p>
                            {item.completed_at && (
                              <p className="text-xs text-muted-foreground mt-1">
                                <CheckCircle className="h-3 w-3 inline mr-1" />
                                Selesai: {formatDate(item.completed_at)}
                              </p>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            {getStatusBadge(item.status_label, item.status)}
                            {item.priority && (
                              <Badge variant={
                                item.priority === 'high' ? 'destructive' : 
                                item.priority === 'medium' ? 'default' : 'secondary'
                              }>
                                {item.priority}
                              </Badge>
                            )}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => updateChecklistStatus(item.id, 'pending')}>
                                  <Clock className="h-4 w-4 mr-2" />
                                  Set Pending
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => updateChecklistStatus(item.id, 'in_progress')}>
                                  <Clock className="h-4 w-4 mr-2 text-yellow-500" />
                                  Set In Progress
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => updateChecklistStatus(item.id, 'completed')}>
                                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                                  Set Completed
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  className="text-red-600"
                                  onClick={() => deleteChecklistItem(item.id, item.description)}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Hapus
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      ))
                    )}
                  </CardContent>
                </Card>
              ))}

              {indicators.length === 0 && (
                <div className="text-center py-12">
                  <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Belum ada indikator</h3>
                  <p className="text-muted-foreground mb-4">
                    Mulai dengan menambahkan indikator untuk mengorganisir checklist
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="documents" className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold">Dokumentasi</h2>
                <p className="text-muted-foreground">File dan dokumen pendukung project</p>
              </div>
              <Button>
                <Upload className="h-4 w-4 mr-2" />
                Upload File
              </Button>
            </div>

            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Belum ada dokumen</h3>
              <p className="text-muted-foreground mb-4">
                Upload file dan dokumen pendukung untuk project ini
              </p>
              <Button>
                <Upload className="h-4 w-4 mr-2" />
                Upload File Pertama
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  )
}
