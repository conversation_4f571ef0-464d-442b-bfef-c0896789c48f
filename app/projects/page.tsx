"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { SidebarTrigger } from "@/components/ui/sidebar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Search, FolderOpen, Calendar, User, MoreHorizontal, Loader2, AlertCircle } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"

// Tambahkan ulang tipe ProjectWithProgress jika perlu
type ProjectWithProgress = {
  id: string
  name: string
  description?: string
  assignee?: string
  status: string
  created_at: string
  progress?: number
  google_sheet_url?: string
  completedChecklist?: number
  totalChecklist?: number
}

export default function ProjectsPage() {
  const [projects, setProjects] = useState<ProjectWithProgress[]>([])
  const [filteredProjects, setFilteredProjects] = useState<ProjectWithProgress[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [newProject, setNewProject] = useState({
    name: "",
    description: "",
    assignee: "",
  })
  const { toast } = useToast()

  // Load projects on component mount
  useEffect(() => {
    loadProjects()
  }, [])

  // Filter projects when search term changes
  useEffect(() => {
    if (searchTerm) {
      const filtered = projects.filter(
        (project) =>
          project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          project.description?.toLowerCase().includes(searchTerm.toLowerCase()),
      )
      setFilteredProjects(filtered)
    } else {
      setFilteredProjects(projects)
    }
  }, [searchTerm, projects])

  const loadProjects = async () => {
    try {
      setIsLoading(true)
      setError(null)
      // Refactor: fetch data project dari API route
      const res = await fetch('/api/projects')
      if (!res.ok) throw new Error('Gagal fetch data project')
      const data = await res.json()
      setProjects(data)
      setFilteredProjects(data)
    } catch (error) {
      console.error('Error loading projects:', error)
      setError('Gagal memuat data project. Silakan coba lagi.')
      toast({
        title: "Error",
        description: "Gagal memuat data project. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateProject = async () => {
    if (!newProject.name || !newProject.description) {
      toast({
        title: "Error",
        description: "Nama project dan deskripsi wajib diisi.",
        variant: "destructive",
      })
      return
    }

    try {
      setIsCreating(true)
      const projectData = {
        name: newProject.name,
        description: newProject.description,
        assignee: newProject.assignee || null,
        status: 'active'
      }
      // Refactor: create project via API route
      const res = await fetch('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(projectData)
      })
      if (!res.ok) throw new Error('Gagal membuat project')
      toast({
        title: "Berhasil",
        description: "Project berhasil dibuat.",
      })
      setNewProject({ name: "", description: "", assignee: "" })
      setIsDialogOpen(false)
      // Reload projects
      await loadProjects()
    } catch (error) {
      console.error('Error creating project:', error)
      toast({
        title: "Error",
        description: "Gagal membuat project. Silakan coba lagi.",
        variant: "destructive",
      })
    } finally {
      setIsCreating(false)
    }
  }

  const handleDeleteProject = async (projectId: string, projectName: string) => {
    if (!confirm(`Apakah Anda yakin ingin menghapus project "${projectName}"?`)) {
      return
    }
    try {
      // Refactor: delete project via API route
      const res = await fetch(`/api/projects/${projectId}`, { method: 'DELETE' })
      if (!res.ok) throw new Error('Gagal menghapus project')
      toast({
        title: "Berhasil",
        description: "Project berhasil dihapus.",
      })
      // Reload projects
      await loadProjects()
    } catch (error) {
      console.error('Error deleting project:', error)
      toast({
        title: "Error",
        description: "Gagal menghapus project. Silakan coba lagi.",
        variant: "destructive",
      })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Memuat data project...</span>
        </div>
      </div>
    )
  }

  return (
    <>
      <header className="flex h-16 shrink-0 items-center justify-between border-b px-4">
        <div className="flex items-center gap-2">
          <SidebarTrigger className="-ml-1" />
          <h1 className="text-2xl font-bold">Project</h1>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Buat Project Baru
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Buat Project Baru</DialogTitle>
              <DialogDescription>Tambahkan project baru untuk mulai mengelola checklist</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Nama Project *</Label>
                <Input
                  id="name"
                  value={newProject.name}
                  onChange={(e) => setNewProject({ ...newProject, name: e.target.value })}
                  placeholder="Masukkan nama project"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Deskripsi *</Label>
                <Textarea
                  id="description"
                  value={newProject.description}
                  onChange={(e) => setNewProject({ ...newProject, description: e.target.value })}
                  placeholder="Deskripsi singkat project"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="assignee">Penanggung Jawab</Label>
                <Input
                  id="assignee"
                  value={newProject.assignee}
                  onChange={(e) => setNewProject({ ...newProject, assignee: e.target.value })}
                  placeholder="Nama penanggung jawab"
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)} disabled={isCreating}>
                Batal
              </Button>
              <Button onClick={handleCreateProject} disabled={isCreating}>
                {isCreating && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                Buat Project
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </header>

      <div className="flex-1 p-6 space-y-6">
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}{" "}
              <Button variant="link" onClick={loadProjects} className="p-0 h-auto">
                Coba lagi
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Search */}
        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cari project..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>

        {/* Projects Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredProjects.map((project) => (
            <Card key={project.id} className="hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-2">
                    <FolderOpen className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">{project.name}</CardTitle>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem asChild>
                        <Link href={`/projects/${project.id}`}>Lihat Detail</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>Edit Project</DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-red-600"
                        onClick={() => handleDeleteProject(project.id, project.name)}
                      >
                        Hapus Project
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <CardDescription className="text-sm">{project.description}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <span>Progress</span>
                  <span className="font-medium">{project.progress}%</span>
                </div>
                <Progress value={project.progress} />

                <div className="flex items-center justify-between text-sm">
                  <span>Checklist</span>
                  <span>
                    {project.completedChecklist}/{project.totalChecklist}
                  </span>
                </div>

                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(project.created_at)}</span>
                  </div>
                  {project.assignee && (
                    <div className="flex items-center space-x-1">
                      <User className="h-4 w-4" />
                      <span>{project.assignee}</span>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <Badge variant={project.status === "active" ? "default" : "secondary"}>
                    {project.status === "active" ? "Aktif" : "Pending"}
                  </Badge>
                  <Button asChild size="sm">
                    <Link href={`/projects/${project.id}`}>Lihat Detail</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProjects.length === 0 && !isLoading && (
          <div className="text-center py-12">
            <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Tidak ada project ditemukan</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm ? "Coba ubah kata kunci pencarian" : "Mulai dengan membuat project baru"}
            </p>
            {!searchTerm && (
              <Button onClick={() => setIsDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Buat Project Baru
              </Button>
            )}
          </div>
        )}
      </div>
    </>
  )
}
