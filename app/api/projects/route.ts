import { NextRequest, NextResponse } from 'next/server'
import { ProjectService } from '@/lib/services/projectService'

export async function GET() {
  try {
    const projects = await ProjectService.getAllProjects()
    return NextResponse.json(projects)
  } catch (error) {
    return NextResponse.json({ error: 'Gagal mengambil data projects' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const project = await ProjectService.createProject(body)
    return NextResponse.json(project)
  } catch (error) {
    return NextResponse.json({ error: 'Gagal membuat project' }, { status: 500 })
  }
} 