import { NextRequest, NextResponse } from 'next/server'
import { IndicatorService } from '@/lib/services/indicatorService'

export async function GET(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const params = await context.params
  try {
    const indicators = await IndicatorService.getIndicatorsByProjectId(params.id)
    return NextResponse.json(indicators)
  } catch (error) {
    return NextResponse.json({ error: 'Gagal mengambil data indikator' }, { status: 500 })
  }
} 
