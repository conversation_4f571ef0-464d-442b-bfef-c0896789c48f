import { NextRequest, NextResponse } from 'next/server'
import { GoogleSheetsService } from '@/lib/services/googleSheetsService'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { projectId, sheetId, action } = body

    // Validate required parameters
    if (!projectId) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'sync-checklist':
        if (!sheetId) {
          return NextResponse.json(
            { error: 'Sheet ID is required for sync-checklist action' },
            { status: 400 }
          )
        }

        const syncResult = await GoogleSheetsService.syncChecklistFromSheet(
          parseInt(projectId),
          sheetId
        )

        if (!syncResult.success) {
          return NextResponse.json(
            { error: syncResult.error },
            { status: 500 }
          )
        }

        return NextResponse.json({
          success: true,
          message: 'Checklist synchronized successfully',
          data: syncResult.data
        })

      case 'get-sheet-url':
        const sheetUrl = await GoogleSheetsService.getProjectSheetUrl(projectId)
        
        return NextResponse.json({
          success: true,
          sheetUrl: sheetUrl
        })

      case 'create-sheet':
        // This would be called manually if sheet creation failed during project creation
        console.log('create-sheet: Starting with projectId:', projectId)
        
        const { GoogleSheetsService: GSService } = await import('@/lib/services/googleSheetsService')
        const { ProjectService } = await import('@/lib/services/projectService')
        
        console.log('create-sheet: Fetching project data...')
        const projectData = await ProjectService.getProjectWithDetails(projectId.toString())
        if (!projectData) {
          console.log('create-sheet: Project not found for ID:', projectId)
          return NextResponse.json(
            { error: 'Project not found' },
            { status: 404 }
          )
        }
        
        // **TAMBAHKAN INI**: Pastikan project ada di master sheet sebelum membuat sheet baru
        console.log('create-sheet: Ensuring project exists in master sheet...')
        const masterSheetResult = await GSService.addProjectToMasterSheet({
          id: projectId,
          name: projectData.name,
          description: projectData.description || undefined,
          status: projectData.status,
          created_at: projectData.created_at
        })

        if (!masterSheetResult.success) {
          console.warn('Could not add project to master sheet, but continuing...', masterSheetResult.error)
          // Jangan hentikan proses, tapi beri peringatan
        }

        console.log('create-sheet: Project found:', projectData.name, 'with', projectData.indicators?.length || 0, 'indicators')

        console.log('create-sheet: Creating Google Sheet...')
        const createResult = await GSService.createProjectSheetFromTemplate({
          id: projectId,
          name: projectData.name,
          indicators: projectData.indicators || []
        })

        console.log('create-sheet: Create result:', createResult)

        if (!createResult.success) {
          console.log('create-sheet: Failed to create sheet:', createResult.error)
          return NextResponse.json(
            { error: createResult.error },
            { status: 500 }
          )
        }

        console.log('create-sheet: Success! Sheet URL:', createResult.sheetUrl)
        return NextResponse.json({
          success: true,
          message: 'Google Sheet created successfully',
          sheetUrl: createResult.sheetUrl
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: sync-checklist, get-sheet-url, or create-sheet' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Error in sync-google-sheets API:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// GET endpoint to check Google Sheets configuration
export async function GET() {
  try {
    const config = GoogleSheetsService.validateConfiguration()
    
    return NextResponse.json({
      configured: config.isValid,
      errors: config.errors,
      message: config.isValid 
        ? 'Google Sheets integration is properly configured'
        : 'Google Sheets integration is not configured'
    })
  } catch (error) {
    return NextResponse.json(
      { 
        configured: false,
        error: 'Failed to check configuration',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
} 