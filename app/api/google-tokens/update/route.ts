import { NextRequest, NextResponse } from 'next/server'
import { GoogleTokenService } from '@/lib/services/googleTokenService'

export async function POST(request: NextRequest) {
  try {
    const { accessToken, refreshToken } = await request.json()

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Access token is required' },
        { status: 400 }
      )
    }

    // Validate the access token with Google API
    const validation = await GoogleTokenService.validateToken(accessToken)

    if (!validation.isValid) {
      return NextResponse.json(
        { error: `Invalid access token: ${validation.error}` },
        { status: 400 }
      )
    }

    // Save tokens to database
    const savedToken = await GoogleTokenService.saveTokens({
      accessToken,
      refreshToken,
      expiresIn: validation.expiresIn,
      scopes: validation.scopes
    })

    if (!savedToken) {
      return NextResponse.json(
        { error: 'Failed to save tokens to database' },
        { status: 500 }
      )
    }

    console.log('✅ Tokens saved to database successfully')
    console.log('Token ID:', savedToken.id)
    console.log('Token scopes:', validation.scopes)
    console.log('Token expires in:', validation.expiresIn, 'seconds')

    return NextResponse.json({
      success: true,
      message: 'Tokens saved successfully to secure database storage.',
      tokenInfo: {
        id: savedToken.id,
        scopes: validation.scopes || [],
        expiresIn: validation.expiresIn,
        hasRefreshToken: !!refreshToken,
        expiresAt: savedToken.expires_at
      }
    })
  } catch (error) {
    console.error('Error updating tokens:', error)
    return NextResponse.json(
      { error: 'Failed to update tokens' },
      { status: 500 }
    )
  }
}