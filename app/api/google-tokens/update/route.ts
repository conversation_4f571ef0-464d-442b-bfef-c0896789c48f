import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { accessToken, refreshToken } = await request.json()

    if (!accessToken) {
      return NextResponse.json(
        { error: 'Access token is required' },
        { status: 400 }
      )
    }

    // Validate the access token
    const validateResponse = await fetch(
      `https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${accessToken}`
    )

    if (!validateResponse.ok) {
      const errorData = await validateResponse.json()
      return NextResponse.json(
        { error: `Invalid access token: ${errorData.error_description}` },
        { status: 400 }
      )
    }

    const tokenData = await validateResponse.json()

    // Note: Dalam production environment, Anda perlu:
    // 1. Update environment variables secara programmatic (jika memungkinkan)
    // 2. Atau simpan di database/secure storage
    // 3. Atau gunakan secret management service
    
    console.log('✅ Token validation successful')
    console.log('Token scopes:', tokenData.scope)
    console.log('Token expires in:', tokenData.expires_in, 'seconds')
    
    // For now, just log the tokens (don't expose in response)
    console.log('New access token:', accessToken.substring(0, 20) + '...')
    if (refreshToken) {
      console.log('New refresh token:', refreshToken.substring(0, 20) + '...')
    }

    return NextResponse.json({
      success: true,
      message: 'Tokens updated successfully. Please update your environment variables.',
      tokenInfo: {
        scopes: tokenData.scope?.split(' ') || [],
        expiresIn: tokenData.expires_in,
        hasRefreshToken: !!refreshToken
      }
    })
  } catch (error) {
    console.error('Error updating tokens:', error)
    return NextResponse.json(
      { error: 'Failed to update tokens' },
      { status: 500 }
    )
  }
}