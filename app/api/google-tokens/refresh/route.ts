import { NextResponse } from 'next/server'

export async function POST() {
  try {
    const refreshToken = process.env.NEXT_PUBLIC_GOOGLE_REFRESH_TOKEN
    const clientId = process.env.GOOGLE_CLIENT_ID
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET

    if (!refreshToken) {
      return NextResponse.json(
        { error: 'Refresh token not available' },
        { status: 400 }
      )
    }

    if (!clientId || !clientSecret) {
      return NextResponse.json(
        { error: 'Google OAuth credentials not configured' },
        { status: 500 }
      )
    }

    const response = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        refresh_token: refreshToken,
        grant_type: 'refresh_token'
      })
    })

    const tokenData = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { error: tokenData.error_description || 'Failed to refresh token' },
        { status: 400 }
      )
    }

    // Note: Dalam production, Anda perlu update environment variables
    // atau simpan di database/secure storage
    console.log('New access token generated:', tokenData.access_token.substring(0, 20) + '...')
    
    return NextResponse.json({
      success: true,
      message: 'Token refreshed successfully',
      expiresIn: tokenData.expires_in,
      // Don't return actual token in response for security
      tokenPreview: tokenData.access_token.substring(0, 20) + '...'
    })
  } catch (error) {
    console.error('Error refreshing token:', error)
    return NextResponse.json(
      { error: 'Failed to refresh token' },
      { status: 500 }
    )
  }
}