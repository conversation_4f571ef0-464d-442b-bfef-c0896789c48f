import { NextResponse } from 'next/server'
import { GoogleTokenService } from '@/lib/services/googleTokenService'

export async function POST() {
  try {
    // Get current active token from database
    const currentToken = await GoogleTokenService.getActiveToken()

    // Fallback to environment variable
    const envRefreshToken = process.env.NEXT_PUBLIC_GOOGLE_REFRESH_TOKEN

    const refreshToken = currentToken?.refresh_token || envRefreshToken
    const clientId = process.env.GOOGLE_CLIENT_ID
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET

    if (!refreshToken) {
      return NextResponse.json(
        { error: 'Refresh token not available in database or environment' },
        { status: 400 }
      )
    }

    if (!clientId || !clientSecret) {
      return NextResponse.json(
        { error: 'Google OAuth credentials not configured' },
        { status: 500 }
      )
    }

    const response = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret,
        refresh_token: refreshToken,
        grant_type: 'refresh_token'
      })
    })

    const tokenData = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        { error: tokenData.error_description || 'Failed to refresh token' },
        { status: 400 }
      )
    }

    // Save the new access token to database
    let savedToken = null
    if (currentToken) {
      // Update existing token
      savedToken = await GoogleTokenService.updateToken(currentToken.id, {
        accessToken: tokenData.access_token,
        expiresIn: tokenData.expires_in
      })
    } else {
      // Create new token entry
      savedToken = await GoogleTokenService.saveTokens({
        accessToken: tokenData.access_token,
        refreshToken: refreshToken,
        expiresIn: tokenData.expires_in
      })
    }

    console.log('✅ Token refreshed and saved to database')
    console.log('New access token generated:', tokenData.access_token.substring(0, 20) + '...')

    return NextResponse.json({
      success: true,
      message: 'Token refreshed and saved to database successfully',
      expiresIn: tokenData.expires_in,
      tokenId: savedToken?.id,
      // Don't return actual token in response for security
      tokenPreview: tokenData.access_token.substring(0, 20) + '...'
    })
  } catch (error) {
    console.error('Error refreshing token:', error)
    return NextResponse.json(
      { error: 'Failed to refresh token' },
      { status: 500 }
    )
  }
}