import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const accessToken = process.env.NEXT_PUBLIC_GOOGLE_OAUTH_TOKEN
    const refreshToken = process.env.NEXT_PUBLIC_GOOGLE_REFRESH_TOKEN

    const tokenInfo = {
      hasToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
      isValid: false,
      error: null as string | null,
      expiresAt: null as string | null,
      scopes: [] as string[]
    }

    if (accessToken) {
      try {
        // Test token validity dengan Google API
        const response = await fetch(
          `https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${accessToken}`
        )
        
        if (response.ok) {
          const tokenData = await response.json()
          tokenInfo.isValid = true
          tokenInfo.expiresAt = new Date(Date.now() + (tokenData.expires_in * 1000)).toISOString()
          tokenInfo.scopes = tokenData.scope?.split(' ') || []
        } else {
          const errorData = await response.json()
          tokenInfo.error = errorData.error_description || 'Token is invalid'
        }
      } catch (error) {
        tokenInfo.error = 'Failed to validate token'
      }
    }

    return NextResponse.json(tokenInfo)
  } catch (error) {
    console.error('Error checking token status:', error)
    return NextResponse.json(
      { error: 'Failed to check token status' },
      { status: 500 }
    )
  }
}