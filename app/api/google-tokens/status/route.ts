import { NextResponse } from 'next/server'
import { GoogleTokenService } from '@/lib/services/googleTokenService'

export async function GET() {
  try {
    // Try to get token from database first
    const dbToken = await GoogleTokenService.getActiveToken()

    // Fallback to environment variables
    const envAccessToken = process.env.NEXT_PUBLIC_GOOGLE_OAUTH_TOKEN
    const envRefreshToken = process.env.NEXT_PUBLIC_GOOGLE_REFRESH_TOKEN

    const tokenInfo = {
      hasToken: !!(dbToken?.access_token || envAccessToken),
      hasRefreshToken: !!(dbToken?.refresh_token || envRefreshToken),
      isValid: false,
      error: null as string | null,
      expiresAt: null as string | null,
      scopes: [] as string[],
      source: 'none' as 'database' | 'environment' | 'none',
      tokenId: null as string | null
    }

    let tokenToValidate: string | null = null

    if (dbToken?.access_token) {
      tokenToValidate = dbToken.access_token
      tokenInfo.source = 'database'
      tokenInfo.tokenId = dbToken.id
      tokenInfo.expiresAt = dbToken.expires_at
      tokenInfo.scopes = dbToken.scopes || []
    } else if (envAccessToken) {
      tokenToValidate = envAccessToken
      tokenInfo.source = 'environment'
    }

    if (tokenToValidate) {
      try {
        // Validate token with Google API
        const validation = await GoogleTokenService.validateToken(tokenToValidate)

        if (validation.isValid) {
          tokenInfo.isValid = true
          if (!tokenInfo.expiresAt && validation.expiresIn) {
            tokenInfo.expiresAt = new Date(Date.now() + (validation.expiresIn * 1000)).toISOString()
          }
          if (validation.scopes && tokenInfo.scopes.length === 0) {
            tokenInfo.scopes = validation.scopes
          }
        } else {
          tokenInfo.error = validation.error || 'Token is invalid'
        }
      } catch (error) {
        tokenInfo.error = 'Failed to validate token'
      }
    }

    return NextResponse.json(tokenInfo)
  } catch (error) {
    console.error('Error checking token status:', error)
    return NextResponse.json(
      { error: 'Failed to check token status' },
      { status: 500 }
    )
  }
}