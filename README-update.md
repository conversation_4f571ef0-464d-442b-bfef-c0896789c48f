# Update Database Schema - Status Label

## Script SQL yang perlu dijalankan di Supabase SQL Editor:

```sql
-- Update Checklist Items Table to Support Status Labels
-- Run this in Supabase SQL Editor after the main schema

-- Add status_label column to checklist_items table
ALTER TABLE public.checklist_items 
ADD COLUMN IF NOT EXISTS status_label VARCHAR(20) DEFAULT 'pending';

-- Update existing records to set appropriate status_label based on current status
UPDATE public.checklist_items 
SET status_label = CASE 
  WHEN status = true THEN 'completed'
  ELSE 'pending'
END
WHERE status_label IS NULL OR status_label = '';

-- Add check constraint for valid status_label values
ALTER TABLE public.checklist_items 
ADD CONSTRAINT check_status_label 
CHECK (status_label IN ('pending', 'in_progress', 'completed'));

-- Create index for better performance on status queries
CREATE INDEX IF NOT EXISTS idx_checklist_items_status_label ON public.checklist_items(status_label);
```

## Fitur Baru yang Ditambahkan:

### 1. Status Checklist yang Lebih Detail
- **Pending**: Status awal checklist item
- **In Progress**: Checklist sedang dikerjakan  
- **Completed**: Checklist sudah selesai

### 2. Dropdown Menu untuk Mengubah Status
- Klik ikon ⋮ (tiga titik) pada setiap checklist item
- Pilih status yang diinginkan:
  - Set Pending
  - Set In Progress  
  - Set Completed

### 3. Visual Indicators
- **Icon**: Menunjukkan status dengan icon yang berbeda
- **Badge**: Label berwarna untuk status (Pending/Progress/Selesai)
- **Priority Badge**: Menampilkan prioritas (High/Medium/Low)

### 4. Fungsi Hapus Checklist
- Opsi "Hapus" di dropdown menu
- Konfirmasi sebelum menghapus
- Update real-time setelah penghapusan

### 5. Kompatibilitas Mundur
- Checkbox masih berfungsi untuk toggle completed/pending
- Data existing tetap kompatibel
- Boolean status tetap tersinkron dengan status_label

## Cara Penggunaan:

1. **Centang Checklist**: Klik checkbox untuk mark as completed
2. **Set Progress**: Klik dropdown menu → "Set In Progress"  
3. **Reset ke Pending**: Klik dropdown menu → "Set Pending"
4. **Hapus Item**: Klik dropdown menu → "Hapus" → Konfirmasi

## Migration Guide:

1. Jalankan script SQL di atas di Supabase SQL Editor
2. Restart development server jika diperlukan
3. Fitur baru akan langsung tersedia 