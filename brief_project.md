# Brief Project: Sistem Manajemen Checklist Project dan Dokumentasi Pendukung

## Deskripsi Singkat
Sistem ini dirancang untuk mengelola checklist kelengkapan dokumen proyek secara terintegrasi dan otomatis menggunakan kombinasi Next.js sebagai frontend, Supabase sebagai backend/database, dan Google Sheets serta Google Docs untuk manajemen dokumen dan kolaborasi.

## Tujuan
- Menyediakan platform yang mudah digunakan untuk memonitor progres setiap proyek secara real-time.
- Mengintegrasikan sistem kolaborasi yang akurat dengan Google Sheets dan Google Docs.
- Memastikan transparansi dan keteraturan dalam dokumentasi setiap proyek.
- Mengurangi kesalahan manual dalam pencatatan dan monitoring checklist proyek.

## Fitur Utama
- **Dashboard Real-time**: Menampilkan progres proyek secara visual (persentase checklist selesai).
- **Manajemen Proyek**: Membuat proyek baru dengan otomatisasi checklist dari template.
- **Integrasi Google Sheets**: Membuat otomatis sheet baru setiap kali proyek ditambahkan, sebagai basis checklist proyek.
- **Sinkronisasi Real-time**: Setiap perubahan status checklist di Google Sheets otomatis tersimpan dan tersinkronisasi ke database Supabase.
- **Dokumentasi Pendukung**: Upload dokumen atau integrasi Google Docs untuk dokumentasi lebih rinci.
- **Laporan dan Ekspor Data**: Menyediakan laporan checklist dalam format PDF atau Excel.

## Cara Kerja Aplikasi secara Spesifik
### Alur Utama
1. User membuat proyek baru melalui antarmuka Next.js.
2. Sistem otomatis menyimpan data ke Supabase serta mencatat proyek baru di Google Sheets utama.
3. Google Sheets utama akan memicu Google Apps Script untuk membuat sheet khusus proyek berdasarkan template.
4. Checklist dalam Google Sheets yang dihasilkan berfungsi sebagai pengelolaan status checklist proyek.
5. Perubahan status checklist otomatis disinkronkan ke database Supabase.
6. Next.js mengambil data dari Supabase secara real-time untuk menampilkan progres visual.
7. Dokumentasi tambahan dapat dikelola melalui integrasi Google Docs.

### Model Data Inti
- **Project**: (ID, nama, deskripsi, tanggal dibuat, URL Google Sheet)
- **Indikator**: (ID, ID proyek, nama indikator)
- **Checklist Item**: (ID, ID indikator, deskripsi checklist, status, tanggal selesai)
- **Dokumen**: (ID, ID proyek, URL file dokumen, tanggal upload)

### Integrasi Teknis
- **Next.js → Supabase**: CRUD data proyek.
- **Supabase → Google Sheets (via Apps Script)**: Otomatisasi pembuatan checklist sheet.
- **Google Sheets → Supabase (via Apps Script)**: Sinkronisasi status checklist.
- **Next.js → Google Docs (Opsional)**: Otomatisasi dokumentasi detail.

Brief ini bertujuan agar AI cursor memahami secara mendalam tentang tujuan, fitur utama, dan cara kerja aplikasi ini secara spesifik untuk keperluan pengembangan selanjutnya.

