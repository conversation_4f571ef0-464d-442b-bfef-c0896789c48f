import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Type definitions for our database
export type Database = {
  public: {
    Tables: {
      projects: {
        Row: {
          id: string
          name: string
          description: string | null
          status: string
          assignee: string | null
          google_sheet_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          status?: string
          assignee?: string | null
          google_sheet_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          status?: string
          assignee?: string | null
          google_sheet_url?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      indicators: {
        Row: {
          id: string
          project_id: string
          name: string
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          project_id: string
          name: string
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          name?: string
          description?: string | null
          created_at?: string
        }
      }
      checklist_items: {
        Row: {
          id: string
          indicator_id: string
          description: string
          status: boolean
          status_label: string | null
          priority: string
          completed_at: string | null
          created_at: string
        }
        Insert: {
          id?: string
          indicator_id: string
          description: string
          status?: boolean
          status_label?: string | null
          priority?: string
          completed_at?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          indicator_id?: string
          description?: string
          status?: boolean
          status_label?: string | null
          priority?: string
          completed_at?: string | null
          created_at?: string
        }
      }
      documents: {
        Row: {
          id: string
          project_id: string
          file_name: string
          file_url: string
          file_size: string | null
          file_type: string | null
          uploaded_by: string | null
          uploaded_at: string
        }
        Insert: {
          id?: string
          project_id: string
          file_name: string
          file_url: string
          file_size?: string | null
          file_type?: string | null
          uploaded_by?: string | null
          uploaded_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          file_name?: string
          file_url?: string
          file_size?: string | null
          file_type?: string | null
          uploaded_by?: string | null
          uploaded_at?: string
        }
      }
      google_tokens: {
        Row: {
          id: string
          access_token: string
          refresh_token: string | null
          expires_at: string | null
          scopes: string[] | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          access_token: string
          refresh_token?: string | null
          expires_at?: string | null
          scopes?: string[] | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          access_token?: string
          refresh_token?: string | null
          expires_at?: string | null
          scopes?: string[] | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}

// Helper types for easier usage
export type Project = Database['public']['Tables']['projects']['Row']
export type ProjectInsert = Database['public']['Tables']['projects']['Insert']
export type ProjectUpdate = Database['public']['Tables']['projects']['Update']

export type Indicator = Database['public']['Tables']['indicators']['Row']
export type IndicatorInsert = Database['public']['Tables']['indicators']['Insert']
export type IndicatorUpdate = Database['public']['Tables']['indicators']['Update']

export type ChecklistItem = Database['public']['Tables']['checklist_items']['Row']
export type ChecklistItemInsert = Database['public']['Tables']['checklist_items']['Insert']
export type ChecklistItemUpdate = Database['public']['Tables']['checklist_items']['Update']

export type Document = Database['public']['Tables']['documents']['Row']
export type DocumentInsert = Database['public']['Tables']['documents']['Insert']
export type DocumentUpdate = Database['public']['Tables']['documents']['Update']

export type GoogleToken = Database['public']['Tables']['google_tokens']['Row']
export type GoogleTokenInsert = Database['public']['Tables']['google_tokens']['Insert']
export type GoogleTokenUpdate = Database['public']['Tables']['google_tokens']['Update']

// Checklist status types
export type ChecklistStatus = 'pending' | 'in_progress' | 'completed'