import { supabase } from '@/lib/supabase'
import { GoogleTokenService } from './googleTokenService'

// Google Sheets API configuration
const GOOGLE_SHEETS_API_BASE = 'https://sheets.googleapis.com/v4/spreadsheets'
const GOOGLE_DRIVE_API_BASE = 'https://www.googleapis.com/drive/v3/files'

// Configuration - these should be in environment variables
const GOOGLE_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_API_KEY
const MASTER_SPREADSHEET_ID = process.env.NEXT_PUBLIC_MASTER_SPREADSHEET_ID
const PROJECT_TEMPLATE_ID = process.env.NEXT_PUBLIC_PROJECT_TEMPLATE_ID
const PROJECTS_FOLDER_ID = process.env.NEXT_PUBLIC_PROJECTS_FOLDER_ID

export interface GoogleSheetsProject {
  projectId: string  // Ubah dari number ke string (UUID)
  projectName: string
  createdDate: string
  sheetUrl?: string
  status: string
}

export interface GoogleSheetsChecklistItem {
  id: number
  indicator: string
  checklistItem: string
  status: string
  completedDate?: string
  documentLink?: string
  priority?: string
}

export interface GoogleSheetsResponse {
  success: boolean
  data?: any
  error?: string
  sheetUrl?: string
  sheetId?: string
  message?: string
}

export class GoogleSheetsService {
  private static async getAuthHeaders() {
    const token = await GoogleTokenService.getTokenForAPI()
    if (!token) {
      throw new Error('No valid Google OAuth token available')
    }

    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }

  private static async createSheetFromTemplate(project: any): Promise<GoogleSheetsResponse> {
    try {
      if (!PROJECT_TEMPLATE_ID || !PROJECTS_FOLDER_ID) {
        throw new Error('Template ID or Folder ID not configured')
      }

      const headers = await this.getAuthHeaders()

      // Copy template to create new sheet
      const copyResponse = await fetch(
        `${GOOGLE_DRIVE_API_BASE}/${PROJECT_TEMPLATE_ID}/copy`,
        {
          method: 'POST',
          headers: headers,
          body: JSON.stringify({
            name: `${project.name} - Checklist`,
            parents: [PROJECTS_FOLDER_ID]
          })
        }
      )

      if (!copyResponse.ok) {
        throw new Error(`Google Drive API error: ${copyResponse.statusText}`)
      }

      const newFile = await copyResponse.json()
      const sheetId = newFile.id
      const sheetUrl = `https://docs.google.com/spreadsheets/d/${sheetId}`

      // Populate with project info
      await this.populateProjectSheet(sheetId, project)

      // Update project with sheet URL
      await this.updateProjectSheetUrl(project.id, sheetUrl)

      console.log(`Template sheet created successfully: ${sheetUrl}`)

      return {
        success: true,
        sheetId: sheetId,
        sheetUrl: sheetUrl,
        message: 'Sheet created from template'
      }
    } catch (error) {
      console.error('Error creating sheet from template:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create sheet from template'
      }
    }
  }

  // Add project to master sheet
  static async addProjectToMasterSheet(project: {
    id: string  // Sudah string UUID, bukan perlu parseInt
    name: string
    description?: string
    status: string
    created_at: string
  }): Promise<GoogleSheetsResponse> {
    try {
      if (!MASTER_SPREADSHEET_ID) {
        throw new Error('Master Spreadsheet ID not configured')
      }
      
      // **TAMBAHKAN INI**: Cek dulu apakah project sudah ada
      const searchResponse = await fetch(`${GOOGLE_SHEETS_API_BASE}/${MASTER_SPREADSHEET_ID}/values/Projects!A:A`, { headers: this.getAuthHeaders() });
      if (searchResponse.ok) {
        const searchData = await searchResponse.json();
        const ids = searchData.values?.flat() || [];
        if (ids.includes(project.id)) {
          console.log(`Project ${project.id} already exists in master sheet. Skipping add.`);
          return { success: true, data: { message: 'Already exists' } };
        }
      } else {
        console.warn('Could not search master sheet to prevent duplicates. Proceeding to add anyway.');
      }

      console.log(`Adding project ${project.name} (${project.id}) to master sheet ${MASTER_SPREADSHEET_ID}`)

      const values = [[
        project.id,  // Kirim UUID langsung, bukan parseInt
        project.name,
        project.description || '',
        project.status,
        new Date(project.created_at).toLocaleDateString('id-ID'),
        '', // Sheet URL will be filled later
        new Date().toISOString()
      ]]

      const response = await fetch(
        `${GOOGLE_SHEETS_API_BASE}/${MASTER_SPREADSHEET_ID}/values/Projects!A:G:append?valueInputOption=RAW&insertDataOption=INSERT_ROWS`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            values: values
          })
        }
      )

      if (!response.ok) {
        const errorBody = await response.text()
        throw new Error(`Google Sheets API error on add. Status: ${response.status}. Body: ${errorBody}`)
      }

      const result = await response.json()
      console.log('Project added to master sheet successfully:', result)

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('Error adding project to master sheet:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Create new project sheet from template
  static async createProjectSheetFromTemplate(project: {
    id: string  // Ubah dari number ke string (UUID)
    name: string
    indicators?: any[]
  }): Promise<GoogleSheetsResponse> {
    try {
      if (!PROJECT_TEMPLATE_ID || !PROJECTS_FOLDER_ID) {
        throw new Error('Template ID or Folder ID not configured')
      }

      // Copy template to create new sheet
      const copyResponse = await fetch(
        `${GOOGLE_DRIVE_API_BASE}/${PROJECT_TEMPLATE_ID}/copy`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            name: `${project.name} - Checklist`,
            parents: [PROJECTS_FOLDER_ID]
          })
        }
      )

      if (!copyResponse.ok) {
        throw new Error(`Google Drive API error: ${copyResponse.statusText}`)
      }

      const newFile = await copyResponse.json()
      const newSheetId = newFile.id
      const sheetUrl = `https://docs.google.com/spreadsheets/d/${newSheetId}/edit`

      // Populate the new sheet with project data
      await this.populateProjectSheet(newSheetId, project)

      // **NEW: Sync template data to Supabase with correct UUIDs**
      await this.syncTemplateDataToSupabase(project.id, newSheetId)

      // Update master sheet with new sheet URL
      await this.updateProjectSheetUrl(project.id, sheetUrl)

      return {
        success: true,
        data: newFile,
        sheetUrl: sheetUrl
      }
    } catch (error) {
      console.error('Error creating project sheet:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Populate new project sheet with checklist items
  static async populateProjectSheet(sheetId: string, project: {
    id: string  // Ubah dari number ke string (UUID)
    name: string
    indicators?: any[]
  }): Promise<GoogleSheetsResponse> {
    try {
      console.log(`Populating sheet ${sheetId} for project ${project.name}`)
      // Update project info in the header
      const headerValues = [
        [`Project: ${project.name}`],
        [`ID: ${project.id}`],
        [`Created: ${new Date().toLocaleDateString('id-ID')}`],
        [''], // Empty row
        ['ID', 'Indikator', 'Checklist Item', 'Status', 'Priority', 'Tanggal Selesai', 'Dokumen Link']
      ]

      const headerUpdateResponse = await fetch(
        `${GOOGLE_SHEETS_API_BASE}/${sheetId}/values/Checklist!A1:G5?valueInputOption=RAW`,
        {
          method: 'PUT',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            values: headerValues
          })
        }
      )

      if (!headerUpdateResponse.ok) {
        const errorBody = await headerUpdateResponse.text()
        throw new Error(`Failed to populate sheet header. Status: ${headerUpdateResponse.status}. Body: ${errorBody}`)
      }
      console.log(`Sheet ${sheetId} header populated successfully.`)

      // Add checklist items if indicators exist
      if (project.indicators && project.indicators.length > 0) {
        const checklistValues: any[][] = []
        let itemId = 1

        for (const indicator of project.indicators) {
          if (indicator.checklist_items && indicator.checklist_items.length > 0) {
            for (const item of indicator.checklist_items) {
              checklistValues.push([
                item.id || itemId++,
                indicator.name,
                item.description,
                item.status ? 'Selesai' : 'Pending',
                item.priority || 'medium',
                item.completed_at ? new Date(item.completed_at).toLocaleDateString('id-ID') : '',
                item.document_link || ''
              ])
            }
          }
        }

        if (checklistValues.length > 0) {
          await fetch(
            `${GOOGLE_SHEETS_API_BASE}/${sheetId}/values/Checklist!A6:G${5 + checklistValues.length}?valueInputOption=RAW`,
            {
              method: 'PUT',
              headers: this.getAuthHeaders(),
              body: JSON.stringify({
                values: checklistValues
              })
            }
          )
        }
      }

      return {
        success: true
      }
    } catch (error) {
      console.error('Error populating project sheet:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Update project sheet URL in master sheet
  static async updateProjectSheetUrl(projectId: string, sheetUrl: string): Promise<GoogleSheetsResponse> {
    try {
      if (!MASTER_SPREADSHEET_ID) {
        throw new Error('Master Spreadsheet ID not configured')
      }
      console.log(`Updating master sheet ${MASTER_SPREADSHEET_ID} for project ${projectId}`)

      // First, find the row with this project ID
      const searchResponse = await fetch(
        `${GOOGLE_SHEETS_API_BASE}/${MASTER_SPREADSHEET_ID}/values/Projects!A:G`,
        {
          headers: this.getAuthHeaders()
        }
      )

      if (!searchResponse.ok) {
        const errorBody = await searchResponse.text()
        throw new Error(`Failed to search master sheet. Status: ${searchResponse.status}. Body: ${errorBody}`)
      }

      const searchData = await searchResponse.json()
      const rows = searchData.values || []
      
      let targetRow = -1
      for (let i = 1; i < rows.length; i++) { // Start from 1 to skip header
        if (rows[i][0] && rows[i][0] === projectId) { // Bandingkan sebagai string, bukan parseInt
          targetRow = i + 1 // Google Sheets is 1-indexed
          break
        }
      }

      if (targetRow === -1) {
        throw new Error('Project not found in master sheet')
      }

      // Update the sheet URL column (column F)
      const updateResponse = await fetch(
        `${GOOGLE_SHEETS_API_BASE}/${MASTER_SPREADSHEET_ID}/values/Projects!F${targetRow}?valueInputOption=RAW`,
        {
          method: 'PUT',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            values: [[sheetUrl]]
          })
        }
      )

      if (!updateResponse.ok) {
        const errorBody = await updateResponse.text()
        throw new Error(`Failed to update sheet URL. Status: ${updateResponse.status}. Body: ${errorBody}`)
      }

      // Also update in Supabase
      await supabase
        .from('projects')
        .update({ google_sheet_url: sheetUrl })
        .eq('id', projectId)

      return {
        success: true,
        sheetUrl: sheetUrl
      }
    } catch (error) {
      console.error('Error updating project sheet URL:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Read checklist data from Google Sheet
  static async getChecklistFromSheet(sheetId: string): Promise<GoogleSheetsResponse> {
    try {
      const response = await fetch(
        `${GOOGLE_SHEETS_API_BASE}/${sheetId}/values/Checklist!A6:G1000`,
        {
          headers: this.getAuthHeaders()
        }
      )

      if (!response.ok) {
        throw new Error(`Failed to read sheet: ${response.statusText}`)
      }

      const data = await response.json()
      const rows = data.values || []

      const checklistItems: GoogleSheetsChecklistItem[] = rows
        .filter((row: any[]) => row[0] && row[1] && row[2]) // Must have ID, indicator, and description
        .map((row: any[]) => ({
          id: parseInt(row[0]) || 0,
          indicator: row[1] || '',
          checklistItem: row[2] || '',
          status: row[3] || 'Pending',
          priority: row[4] || 'medium',
          completedDate: row[5] || '',
          documentLink: row[6] || ''
        }))

      return {
        success: true,
        data: checklistItems
      }
    } catch (error) {
      console.error('Error reading checklist from sheet:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Update checklist item status in Google Sheet
  static async updateChecklistItemInSheet(
    sheetId: string,
    itemId: number,
    updates: {
      status?: string
      priority?: string
      completedDate?: string
      // REMOVED: documentLink?: string
    }
  ): Promise<GoogleSheetsResponse> {
    try {
      const checklistResponse = await this.getChecklistFromSheet(sheetId)
      if (!checklistResponse.success || !checklistResponse.data) {
        throw new Error('Failed to read current checklist')
      }

      const items = checklistResponse.data as GoogleSheetsChecklistItem[]
      const itemIndex = items.findIndex(item => item.id === itemId)
      
      if (itemIndex === -1) {
        throw new Error('Checklist item not found')
      }

      const currentItem = items[itemIndex]
      const rowNumber = itemIndex + 6 // Start from row 6

      // Update values (remove document_link column)
      const updateValues = [
        currentItem.id,
        currentItem.indicator,
        currentItem.checklistItem,
        updates.status || currentItem.status,
        updates.priority || currentItem.priority,
        updates.completedDate || currentItem.completedDate || '',
        '' // Keep empty for document link column in sheet
      ]

      const response = await fetch(
        `${GOOGLE_SHEETS_API_BASE}/${sheetId}/values/Checklist!A${rowNumber}:G${rowNumber}?valueInputOption=RAW`,
        {
          method: 'PUT',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            values: [updateValues]
          })
        }
      )

      if (!response.ok) {
        throw new Error(`Failed to update sheet: ${response.statusText}`)
      }

      return {
        success: true,
        data: updateValues
      }
    } catch (error) {
      console.error('Error updating checklist item in sheet:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Sync checklist status from Google Sheet to Supabase
  static async syncChecklistFromSheet(projectId: number, sheetId: string): Promise<GoogleSheetsResponse> {
    try {
      const sheetResponse = await this.getChecklistFromSheet(sheetId)
      if (!sheetResponse.success || !sheetResponse.data) {
        throw new Error('Failed to read checklist from sheet')
      }

      const sheetItems = sheetResponse.data as GoogleSheetsChecklistItem[]
      const updates: any[] = []

      for (const sheetItem of sheetItems) {
        // Update in Supabase - REMOVE document_link field
        const { error } = await supabase
          .from('checklist_items')
          .update({
            status: sheetItem.status === 'Selesai',
            status_label: sheetItem.status === 'Selesai' ? 'completed' : 
                         sheetItem.status === 'Progress' ? 'in_progress' : 'pending',
            priority: sheetItem.priority,
            completed_at: sheetItem.status === 'Selesai' && sheetItem.completedDate ? 
                         new Date(sheetItem.completedDate).toISOString() : null
            // REMOVED: document_link: sheetItem.documentLink || null
          })
          .eq('id', sheetItem.id)

        if (!error) {
          updates.push({
            id: sheetItem.id,
            status: sheetItem.status,
            synced: true
          })
        } else {
          console.error(`Failed to update item ${sheetItem.id}:`, error)
          updates.push({
            id: sheetItem.id,
            status: sheetItem.status,
            synced: false,
            error: error.message
          })
        }
      }

      return {
        success: true,
        data: {
          totalItems: sheetItems.length,
          updates: updates,
          successCount: updates.filter(u => u.synced).length,
          errorCount: updates.filter(u => !u.synced).length
        }
      }
    } catch (error) {
      console.error('Error syncing checklist from sheet:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Get Google Sheet URL for a project
  static async getProjectSheetUrl(projectId: string): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('google_sheet_url')
        .eq('id', projectId)
        .single()

      if (error || !data) {
        return null
      }

      return data.google_sheet_url
    } catch (error) {
      console.error('Error getting project sheet URL:', error)
      return null
    }
  }

  // Validate Google Sheets API configuration
  static async validateConfiguration(): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []

    if (!GOOGLE_API_KEY) {
      errors.push('Google API Key not configured')
    }

    const token = await GoogleTokenService.getTokenForAPI()
    if (!token) {
      errors.push('Google OAuth Token not available in database or environment')
    }
    if (!MASTER_SPREADSHEET_ID) {
      errors.push('Master Spreadsheet ID not configured')
    }
    if (!PROJECT_TEMPLATE_ID) {
      errors.push('Project Template ID not configured')
    }
    if (!PROJECTS_FOLDER_ID) {
      errors.push('Projects Folder ID not configured')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // Extract sheet ID from Google Sheets URL
  static extractSheetIdFromUrl(url: string): string | null {
    const match = url.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/)
    return match ? match[1] : null
  }

  // Generate Google Apps Script code for webhook
  static generateAppsScriptCode(supabaseUrl: string, supabaseKey: string): string {
    return `
function onEdit(e) {
  var sheet = e.source.getActiveSheet();
  var range = e.range;
  
  // Only trigger on Checklist sheet and Status column (column D)
  if (sheet.getName() === "Checklist" && range.getColumn() === 4) {
    var status = range.getValue();
    var row = range.getRow();
    
    // Skip header rows
    if (row < 6) return;
    
    var checklistId = sheet.getRange(row, 1).getValue();
    var completedDate = status === "Selesai" ? new Date().toLocaleDateString('id-ID') : "";
    
    // Update completed date in sheet
    if (status === "Selesai") {
      sheet.getRange(row, 6).setValue(completedDate);
    }
    
    // Sync to Supabase
    var payload = {
      "status": status === "Selesai",
      "status_label": status === "Selesai" ? "completed" : 
                     status === "Progress" ? "in_progress" : "pending",
      "completed_at": status === "Selesai" ? new Date().toISOString() : null
    };

    var options = {
      "method": "PATCH",
      "contentType": "application/json",
      "payload": JSON.stringify(payload),
      "headers": {
        "apikey": "${supabaseKey}",
        "Authorization": "Bearer ${supabaseKey}",
        "Content-Type": "application/json"
      }
    };

    try {
      var response = UrlFetchApp.fetch(
        "${supabaseUrl}/rest/v1/checklist_items?id=eq." + checklistId, 
        options
      );
      
      if (response.getResponseCode() !== 200) {
        console.error("Failed to sync to Supabase:", response.getContentText());
      }
    } catch (error) {
      console.error("Error syncing to Supabase:", error);
    }
  }
}

function manualSync() {
  var sheet = SpreadsheetApp.getActiveSheet();
  var data = sheet.getRange("A6:G1000").getValues();
  
  for (var i = 0; i < data.length; i++) {
    var row = data[i];
    if (row[0] && row[1] && row[2]) { // Has ID, indicator, and item
      var checklistId = row[0];
      var status = row[3];
      
      var payload = {
        "status": status === "Selesai",
        "status_label": status === "Selesai" ? "completed" : 
                       status === "Progress" ? "in_progress" : "pending",
        "completed_at": status === "Selesai" && row[5] ? new Date(row[5]).toISOString() : null
      };

      var options = {
        "method": "PATCH",
        "contentType": "application/json",
        "payload": JSON.stringify(payload),
        "headers": {
          "apikey": "${supabaseKey}",
          "Authorization": "Bearer ${supabaseKey}",
          "Content-Type": "application/json"
        }
      };

      try {
        UrlFetchApp.fetch(
          "${supabaseUrl}/rest/v1/checklist_items?id=eq." + checklistId, 
          options
        );
      } catch (error) {
        console.error("Error syncing item " + checklistId + ":", error);
      }
    }
  }
}`;
  }

  // NEW: Sync template data from Google Sheet to Supabase
  private static async syncTemplateDataToSupabase(projectId: string, sheetId: string): Promise<void> {
    try {
      console.log(`🔄 Syncing template data to Supabase for project ${projectId}`)
      
      // Read template data from Google Sheet
      const response = await fetch(
        `${GOOGLE_SHEETS_API_BASE}/${sheetId}/values/Checklist!A6:G1000`,
        {
          method: 'GET',
          headers: this.getAuthHeaders()
        }
      )

      if (!response.ok) {
        throw new Error(`Failed to read template data: ${response.statusText}`)
      }

      const data = await response.json()
      const rows = data.values || []

      if (rows.length === 0) {
        console.log('No template data to sync')
        return
      }

      // Group by indicators (ignore template IDs, use indicator names)
      const indicatorMap = new Map<string, any[]>()
      
      for (const row of rows) {
        if (row[1] && row[2]) { // Has indicator name and item description
          const indicatorName = row[1]
          
          if (!indicatorMap.has(indicatorName)) {
            indicatorMap.set(indicatorName, [])
          }
          
          indicatorMap.get(indicatorName)!.push({
            description: row[2],
            status: row[3] === 'Selesai',
            status_label: row[3] === 'Selesai' ? 'completed' : 
                         row[3] === 'Progress' ? 'in_progress' : 'pending',
            priority: row[4] || 'medium'
          })
        }
      }

      console.log(`📊 Found ${indicatorMap.size} indicators in template`)

      // Create indicators and checklist items in Supabase
      const createdIndicators = []
      for (const [indicatorName, items] of indicatorMap) {
        const indicator = await this.createIndicatorWithItems(projectId, indicatorName, items)
        createdIndicators.push(indicator)
      }

      // Update Google Sheet with correct UUIDs
      await this.updateSheetWithCorrectIds(sheetId, createdIndicators)

      console.log(`✅ Successfully synced ${indicatorMap.size} indicators to Supabase`)
    } catch (error) {
      console.error('❌ Error syncing template data to Supabase:', error)
      // Don't throw error to avoid breaking sheet creation
    }
  }

  // NEW: Create indicator with checklist items
  private static async createIndicatorWithItems(projectId: string, indicatorName: string, items: any[]) {
    try {
      // Create indicator
      const { data: indicator, error: indicatorError } = await supabase
        .from('indicators')
        .insert({
          project_id: projectId,
          name: indicatorName,
          description: `Indikator ${indicatorName}`
        })
        .select()
        .single()

      if (indicatorError) {
        throw new Error(`Failed to create indicator: ${indicatorError.message}`)
      }

      // Create checklist items
      const checklistItems = items.map(item => ({
        indicator_id: indicator.id,
        description: item.description,
        status: item.status,
        status_label: item.status_label,
        priority: item.priority || 'medium'
      }))

      const { data: createdItems, error: itemsError } = await supabase
        .from('checklist_items')
        .insert(checklistItems)
        .select()

      if (itemsError) {
        throw new Error(`Failed to create checklist items: ${itemsError.message}`)
      }

      console.log(`📝 Created indicator "${indicatorName}" with ${items.length} checklist items`)
      
      return {
        indicator,
        checklist_items: createdItems
      }
    } catch (error) {
      console.error(`❌ Error creating indicator "${indicatorName}":`, error)
      throw error
    }
  }

  // NEW: Update Google Sheet with correct UUIDs
  private static async updateSheetWithCorrectIds(sheetId: string, createdIndicators: any[]): Promise<void> {
    try {
      console.log('🔄 Updating Google Sheet with correct UUIDs...')
      
      // Prepare updated data with correct IDs
      const updatedRows = []
      
      for (const indicatorData of createdIndicators) {
        const { indicator, checklist_items } = indicatorData
        
        for (const item of checklist_items) {
          updatedRows.push([
            item.id, // Correct UUID from Supabase
            indicator.name,
            item.description,
            item.status ? 'Selesai' : item.status_label === 'in_progress' ? 'Progress' : 'Pending',
            item.priority || 'medium',
            item.completed_at ? new Date(item.completed_at).toLocaleDateString('id-ID') : '',
            '' // Document link (empty for now)
          ])
        }
      }

      if (updatedRows.length === 0) {
        console.log('No rows to update')
        return
      }

      // Clear existing data first
      await fetch(
        `${GOOGLE_SHEETS_API_BASE}/${sheetId}/values/Checklist!A6:G1000:clear`,
        {
          method: 'POST',
          headers: this.getAuthHeaders()
        }
      )

      // Insert updated data with correct UUIDs
      const response = await fetch(
        `${GOOGLE_SHEETS_API_BASE}/${sheetId}/values/Checklist!A6:G${5 + updatedRows.length}?valueInputOption=RAW`,
        {
          method: 'PUT',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            values: updatedRows
          })
        }
      )

      if (!response.ok) {
        throw new Error(`Failed to update sheet with correct IDs: ${response.statusText}`)
      }

      console.log(`✅ Successfully updated Google Sheet with ${updatedRows.length} rows with correct UUIDs`)
    } catch (error) {
      console.error('❌ Error updating sheet with correct IDs:', error)
      // Don't throw to avoid breaking the sync process
    }
  }
}
