import { supabase } from '@/lib/supabase'
import type { GoogleToken, GoogleTokenInsert, GoogleTokenUpdate } from '@/lib/supabase'

export class GoogleTokenService {
  /**
   * Get the currently active Google token
   */
  static async getActiveToken(): Promise<GoogleToken | null> {
    try {
      const { data, error } = await supabase
        .from('google_tokens')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error fetching active token:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in getActiveToken:', error)
      return null
    }
  }

  /**
   * Save new Google tokens to database
   */
  static async saveTokens(tokenData: {
    accessToken: string
    refreshToken?: string
    expiresIn?: number
    scopes?: string[]
  }): Promise<GoogleToken | null> {
    try {
      // First, deactivate all existing tokens
      await supabase
        .from('google_tokens')
        .update({ is_active: false })
        .eq('is_active', true)

      // Calculate expiration time
      const expiresAt = tokenData.expiresIn 
        ? new Date(Date.now() + (tokenData.expiresIn * 1000)).toISOString()
        : null

      // Insert new token
      const insertData: GoogleTokenInsert = {
        access_token: tokenData.accessToken,
        refresh_token: tokenData.refreshToken || null,
        expires_at: expiresAt,
        scopes: tokenData.scopes || null,
        is_active: true
      }

      const { data, error } = await supabase
        .from('google_tokens')
        .insert(insertData)
        .select()
        .single()

      if (error) {
        console.error('Error saving tokens:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in saveTokens:', error)
      return null
    }
  }

  /**
   * Update existing token (usually after refresh)
   */
  static async updateToken(
    tokenId: string, 
    updates: {
      accessToken?: string
      refreshToken?: string
      expiresIn?: number
      scopes?: string[]
    }
  ): Promise<GoogleToken | null> {
    try {
      const updateData: GoogleTokenUpdate = {}

      if (updates.accessToken) {
        updateData.access_token = updates.accessToken
      }
      
      if (updates.refreshToken) {
        updateData.refresh_token = updates.refreshToken
      }
      
      if (updates.expiresIn) {
        updateData.expires_at = new Date(Date.now() + (updates.expiresIn * 1000)).toISOString()
      }
      
      if (updates.scopes) {
        updateData.scopes = updates.scopes
      }

      const { data, error } = await supabase
        .from('google_tokens')
        .update(updateData)
        .eq('id', tokenId)
        .select()
        .single()

      if (error) {
        console.error('Error updating token:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in updateToken:', error)
      return null
    }
  }

  /**
   * Check if current token is expired or about to expire
   */
  static async isTokenExpired(token?: GoogleToken): Promise<boolean> {
    try {
      const currentToken = token || await this.getActiveToken()
      
      if (!currentToken || !currentToken.expires_at) {
        return true
      }

      const expirationTime = new Date(currentToken.expires_at).getTime()
      const currentTime = Date.now()
      const bufferTime = 5 * 60 * 1000 // 5 minutes buffer

      return (expirationTime - currentTime) <= bufferTime
    } catch (error) {
      console.error('Error checking token expiration:', error)
      return true
    }
  }

  /**
   * Get token for API calls (with fallback to environment variables)
   */
  static async getTokenForAPI(): Promise<string | null> {
    try {
      // First try to get from database
      const dbToken = await this.getActiveToken()
      
      if (dbToken && !await this.isTokenExpired(dbToken)) {
        return dbToken.access_token
      }

      // Fallback to environment variable
      const envToken = process.env.NEXT_PUBLIC_GOOGLE_OAUTH_TOKEN
      if (envToken) {
        console.warn('Using fallback environment token')
        return envToken
      }

      return null
    } catch (error) {
      console.error('Error getting token for API:', error)
      return null
    }
  }

  /**
   * Validate token with Google API
   */
  static async validateToken(accessToken: string): Promise<{
    isValid: boolean
    expiresIn?: number
    scopes?: string[]
    error?: string
  }> {
    try {
      const response = await fetch(
        `https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=${accessToken}`
      )

      if (response.ok) {
        const tokenData = await response.json()
        return {
          isValid: true,
          expiresIn: tokenData.expires_in,
          scopes: tokenData.scope?.split(' ') || []
        }
      } else {
        const errorData = await response.json()
        return {
          isValid: false,
          error: errorData.error_description || 'Token is invalid'
        }
      }
    } catch (error) {
      return {
        isValid: false,
        error: 'Failed to validate token'
      }
    }
  }

  /**
   * Get all tokens (for admin/debugging purposes)
   */
  static async getAllTokens(): Promise<GoogleToken[]> {
    try {
      const { data, error } = await supabase
        .from('google_tokens')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching all tokens:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getAllTokens:', error)
      return []
    }
  }

  /**
   * Delete old inactive tokens (cleanup)
   */
  static async cleanupOldTokens(keepCount: number = 5): Promise<void> {
    try {
      const { data: tokensToKeep } = await supabase
        .from('google_tokens')
        .select('id')
        .order('created_at', { ascending: false })
        .limit(keepCount)

      if (tokensToKeep && tokensToKeep.length > 0) {
        const idsToKeep = tokensToKeep.map(t => t.id)
        
        await supabase
          .from('google_tokens')
          .delete()
          .not('id', 'in', `(${idsToKeep.join(',')})`)
      }
    } catch (error) {
      console.error('Error cleaning up old tokens:', error)
    }
  }
}
