import { supabase } from '@/lib/supabase'
import type { Indicator, IndicatorInsert, IndicatorUpdate, ChecklistItem, ChecklistItemInsert, ChecklistItemUpdate, ChecklistStatus } from '@/lib/supabase'

export interface IndicatorWithChecklist extends Indicator {
  checklist_items: ChecklistItem[]
}

export class IndicatorService {
  // Get indicators with checklist items by project ID
  static async getIndicatorsByProjectId(projectId: string): Promise<IndicatorWithChecklist[]> {
    try {
      const { data, error } = await supabase
        .from('indicators')
        .select(`
          *,
          checklist_items (*)
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: true })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching indicators:', error)
      throw error
    }
  }

  // Create new indicator
  static async createIndicator(indicatorData: IndicatorInsert): Promise<Indicator> {
    try {
      const { data, error } = await supabase
        .from('indicators')
        .insert([indicatorData])
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating indicator:', error)
      throw error
    }
  }

  // Update indicator
  static async updateIndicator(id: string, updates: IndicatorUpdate): Promise<Indicator> {
    try {
      const { data, error } = await supabase
        .from('indicators')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating indicator:', error)
      throw error
    }
  }

  // Delete indicator
  static async deleteIndicator(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('indicators')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting indicator:', error)
      throw error
    }
  }
}

export class ChecklistItemService {
  // Create new checklist item
  static async createChecklistItem(checklistData: ChecklistItemInsert): Promise<ChecklistItem> {
    try {
      const { data, error } = await supabase
        .from('checklist_items')
        .insert([{
          ...checklistData,
          status_label: 'pending'
        }])
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating checklist item:', error)
      throw error
    }
  }

  // Update checklist item
  static async updateChecklistItem(id: string, updates: ChecklistItemUpdate): Promise<ChecklistItem> {
    try {
      const { data, error } = await supabase
        .from('checklist_items')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating checklist item:', error)
      throw error
    }
  }

  // Update checklist status with status label
  static async updateChecklistStatus(id: string, statusLabel: ChecklistStatus): Promise<ChecklistItem> {
    try {
      const updates: ChecklistItemUpdate = {
        status_label: statusLabel,
        status: statusLabel === 'completed',
        completed_at: statusLabel === 'completed' ? new Date().toISOString() : null
      }

      const { data, error } = await supabase
        .from('checklist_items')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating checklist status:', error)
      throw error
    }
  }

  // Toggle checklist item status (for backward compatibility)
  static async toggleChecklistStatus(id: string): Promise<ChecklistItem> {
    try {
      // First get current status
      const { data: currentData, error: fetchError } = await supabase
        .from('checklist_items')
        .select('status, status_label')
        .eq('id', id)
        .single()

      if (fetchError) throw fetchError

      // Determine next status based on current status_label
      let nextStatusLabel: ChecklistStatus
      if (currentData.status_label === 'pending') {
        nextStatusLabel = 'completed'
      } else if (currentData.status_label === 'in_progress') {
        nextStatusLabel = 'completed'
      } else {
        nextStatusLabel = 'pending'
      }

      return this.updateChecklistStatus(id, nextStatusLabel)
    } catch (error) {
      console.error('Error toggling checklist status:', error)
      throw error
    }
  }

  // Delete checklist item
  static async deleteChecklistItem(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('checklist_items')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting checklist item:', error)
      throw error
    }
  }
} 