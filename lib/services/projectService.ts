import { supabase } from '@/lib/supabase'
import type { Project, ProjectInsert, ProjectUpdate } from '@/lib/supabase'

export interface ProjectWithProgress extends Project {
  totalChecklist: number
  completedChecklist: number
  progress: number
}

export class ProjectService {
  // Get all projects with progress calculation
  static async getAllProjects(): Promise<ProjectWithProgress[]> {
    try {
      const { data: projects, error } = await supabase
        .from('projects')
        .select(`
          *,
          indicators (
            id,
            checklist_items (
              id,
              status
            )
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Calculate progress for each project
      const projectsWithProgress: ProjectWithProgress[] = projects.map(project => {
        let totalChecklist = 0
        let completedChecklist = 0

        // Count checklist items from all indicators
        project.indicators?.forEach((indicator: any) => {
          if (indicator.checklist_items) {
            totalChecklist += indicator.checklist_items.length
            completedChecklist += indicator.checklist_items.filter((item: any) => item.status === true).length
          }
        })

        const progress = totalChecklist > 0 ? Math.round((completedChecklist / totalChecklist) * 100) : 0

        return {
          ...project,
          totalChecklist,
          completedChecklist,
          progress
        }
      })

      return projectsWithProgress
    } catch (error) {
      console.error('Error fetching projects:', error)
      throw error
    }
  }

  // Get single project by ID
  static async getProjectById(id: string): Promise<Project | null> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching project:', error)
      throw error
    }
  }

  // Create new project
  static async createProject(projectData: ProjectInsert): Promise<Project> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single()

      if (error) throw error

      // After creating project, integrate with Google Sheets
      await this.createGoogleSheetIntegration(data)

      return data
    } catch (error) {
      console.error('Error creating project:', error)
      throw error
    }
  }

  // Create Google Sheet integration for project
  static async createGoogleSheetIntegration(project: Project): Promise<void> {
    try {
      // Import Google Sheets service dynamically to avoid server-side issues
      const { GoogleSheetsService } = await import('./googleSheetsService')
      
      // Check if Google Sheets is configured
      const config = GoogleSheetsService.validateConfiguration()
      if (!config.isValid) {
        console.warn('Google Sheets not configured, skipping integration:', config.errors)
        return
      }

      // Add project to master sheet
      const masterSheetResult = await GoogleSheetsService.addProjectToMasterSheet({
        id: project.id,  // Hapus parseInt(), karena project.id sudah UUID string
        name: project.name,
        description: project.description || undefined,
        status: project.status,
        created_at: project.created_at
      })

      if (!masterSheetResult.success) {
        console.error('Failed to add project to master sheet:', masterSheetResult.error)
        return
      }

      // Get project with indicators for sheet creation
      const projectWithIndicators = await this.getProjectWithDetails(project.id)
      
      // Create project-specific sheet from template
      const sheetResult = await GoogleSheetsService.createProjectSheetFromTemplate({
        id: project.id,  // Hapus parseInt(), karena project.id sudah UUID string
        name: project.name,
        indicators: projectWithIndicators?.indicators || []
      })

      if (sheetResult.success && sheetResult.sheetUrl) {
        console.log(`Google Sheet created for project ${project.name}: ${sheetResult.sheetUrl}`)
      } else {
        console.error('Failed to create project sheet:', sheetResult.error)
      }
    } catch (error) {
      console.error('Error creating Google Sheet integration:', error)
      // Don't throw error to avoid breaking project creation
    }
  }

  // Get project with detailed indicators and checklist items
  static async getProjectWithDetails(id: string): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          indicators (
            id,
            name,
            description,
            checklist_items (
              id,
              description,
              status,
              status_label,
              priority,
              completed_at
            )
          )
        `)
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error fetching project details:', error)
      throw error
    }
  }

  // Update project
  static async updateProject(id: string, updates: ProjectUpdate): Promise<Project> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating project:', error)
      throw error
    }
  }

  // Delete project
  static async deleteProject(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting project:', error)
      throw error
    }
  }

  // Search projects
  static async searchProjects(searchTerm: string): Promise<ProjectWithProgress[]> {
    try {
      const { data: projects, error } = await supabase
        .from('projects')
        .select(`
          *,
          indicators (
            id,
            checklist_items (
              id,
              status
            )
          )
        `)
        .or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Calculate progress for each project
      const projectsWithProgress: ProjectWithProgress[] = projects.map(project => {
        let totalChecklist = 0
        let completedChecklist = 0

        project.indicators?.forEach((indicator: any) => {
          if (indicator.checklist_items) {
            totalChecklist += indicator.checklist_items.length
            completedChecklist += indicator.checklist_items.filter((item: any) => item.status === true).length
          }
        })

        const progress = totalChecklist > 0 ? Math.round((completedChecklist / totalChecklist) * 100) : 0

        return {
          ...project,
          totalChecklist,
          completedChecklist,
          progress
        }
      })

      return projectsWithProgress
    } catch (error) {
      console.error('Error searching projects:', error)
      throw error
    }
  }
} 