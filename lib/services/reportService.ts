import { supabase } from '@/lib/supabase'
import type { Project } from '@/lib/supabase'

export interface ProjectReportData {
  id: number
  name: string
  description: string | null
  status: string
  created_at: string
  updated_at: string
  totalChecklist: number
  completedChecklist: number
  inProgressChecklist: number
  pendingChecklist: number
  progress: number
  indicators: IndicatorReport[]
  documents: DocumentReport[]
}

export interface IndicatorReport {
  id: number
  name: string
  totalChecklist: number
  completedChecklist: number
  inProgressChecklist: number
  pendingChecklist: number
  progress: number
}

export interface DocumentReport {
  id: number
  file_name: string
  file_size: string
  uploaded_at: string
}

export interface ReportSummary {
  totalProjects: number
  activeProjects: number
  completedProjects: number
  totalChecklists: number
  completedChecklists: number
  inProgressChecklists: number
  pendingChecklists: number
  averageProgress: number
  totalDocuments: number
  totalStorageSize: number
}

export interface ProjectProgressChart {
  name: string
  completed: number
  inProgress: number
  pending: number
  total: number
  progress: number
}

export interface StatusDistributionChart {
  name: string
  value: number
  count: number
  color: string
}

export interface MonthlyCompletionChart {
  month: string
  completed: number
  created: number
}

export interface PriorityDistributionChart {
  priority: string
  count: number
  percentage: number
  color: string
}

export interface DocumentTypeChart {
  type: string
  count: number
  size: number
  color: string
}

export class ReportService {
  // Get comprehensive project reports
  static async getProjectReports(projectId?: number): Promise<ProjectReportData[]> {
    try {
      let query = supabase
        .from('projects')
        .select(`
          *,
          indicators (
            id,
            name,
            checklist_items (
              id,
              status,
              status_label,
              priority,
              created_at,
              completed_at
            )
          ),
          documents (
            id,
            file_name,
            file_size,
            uploaded_at
          )
        `)
        .order('created_at', { ascending: false })

      if (projectId) {
        query = query.eq('id', projectId)
      }

      const { data: projects, error } = await query

      if (error) throw error

      return (projects || []).map(project => {
        let totalChecklist = 0
        let completedChecklist = 0
        let inProgressChecklist = 0
        let pendingChecklist = 0

        const indicators: IndicatorReport[] = (project.indicators || []).map((indicator: any) => {
          const items = indicator.checklist_items || []
          const indicatorCompleted = items.filter((item: any) => item.status === true).length
          const indicatorInProgress = items.filter((item: any) => item.status_label === 'in_progress').length
          const indicatorPending = items.filter((item: any) => 
            item.status_label === 'pending' || 
            (item.status_label === null && item.status === false)
          ).length
          const indicatorTotal = items.length

          totalChecklist += indicatorTotal
          completedChecklist += indicatorCompleted
          inProgressChecklist += indicatorInProgress
          pendingChecklist += indicatorPending

          return {
            id: indicator.id,
            name: indicator.name,
            totalChecklist: indicatorTotal,
            completedChecklist: indicatorCompleted,
            inProgressChecklist: indicatorInProgress,
            pendingChecklist: indicatorPending,
            progress: indicatorTotal > 0 ? Math.round((indicatorCompleted / indicatorTotal) * 100) : 0
          }
        })

        const documents: DocumentReport[] = (project.documents || []).map((doc: any) => ({
          id: doc.id,
          file_name: doc.file_name,
          file_size: doc.file_size,
          uploaded_at: doc.uploaded_at
        }))

        const progress = totalChecklist > 0 ? Math.round((completedChecklist / totalChecklist) * 100) : 0

        return {
          ...project,
          totalChecklist,
          completedChecklist,
          inProgressChecklist,
          pendingChecklist,
          progress,
          indicators,
          documents
        }
      })
    } catch (error) {
      console.error('Error fetching project reports:', error)
      throw error
    }
  }

  // Get report summary statistics
  static async getReportSummary(): Promise<ReportSummary> {
    try {
      const projectReports = await this.getProjectReports()

      const totalProjects = projectReports.length
      const activeProjects = projectReports.filter(p => p.status === 'active').length
      const completedProjects = projectReports.filter(p => p.progress === 100).length

      const totalChecklists = projectReports.reduce((sum, p) => sum + p.totalChecklist, 0)
      const completedChecklists = projectReports.reduce((sum, p) => sum + p.completedChecklist, 0)
      const inProgressChecklists = projectReports.reduce((sum, p) => sum + p.inProgressChecklist, 0)
      const pendingChecklists = projectReports.reduce((sum, p) => sum + p.pendingChecklist, 0)

      const averageProgress = totalProjects > 0 
        ? Math.round(projectReports.reduce((sum, p) => sum + p.progress, 0) / totalProjects)
        : 0

      const totalDocuments = projectReports.reduce((sum, p) => sum + p.documents.length, 0)
      const totalStorageSize = projectReports.reduce((sum, p) => {
        return sum + p.documents.reduce((docSum, doc) => {
          const size = parseFloat(doc.file_size?.replace(' MB', '') || '0')
          return docSum + size
        }, 0)
      }, 0)

      return {
        totalProjects,
        activeProjects,
        completedProjects,
        totalChecklists,
        completedChecklists,
        inProgressChecklists,
        pendingChecklists,
        averageProgress,
        totalDocuments,
        totalStorageSize: Math.round(totalStorageSize * 100) / 100
      }
    } catch (error) {
      console.error('Error fetching report summary:', error)
      throw error
    }
  }

  // Get project progress chart data
  static async getProjectProgressChart(): Promise<ProjectProgressChart[]> {
    try {
      const projectReports = await this.getProjectReports()

      return projectReports.map(project => ({
        name: project.name.length > 15 ? project.name.substring(0, 15) + '...' : project.name,
        completed: project.completedChecklist,
        inProgress: project.inProgressChecklist,
        pending: project.pendingChecklist,
        total: project.totalChecklist,
        progress: project.progress
      }))
    } catch (error) {
      console.error('Error fetching project progress chart:', error)
      throw error
    }
  }

  // Get status distribution chart data
  static async getStatusDistributionChart(): Promise<StatusDistributionChart[]> {
    try {
      const { data: checklists, error } = await supabase
        .from('checklist_items')
        .select('status, status_label')

      if (error) throw error

      const items = checklists || []
      const total = items.length

      if (total === 0) {
        return [
          { name: 'Pending', value: 0, count: 0, color: '#9ca3af' },
          { name: 'In Progress', value: 0, count: 0, color: '#f59e0b' },
          { name: 'Selesai', value: 0, count: 0, color: '#22c55e' }
        ]
      }

      const completed = items.filter(item => item.status === true).length
      const inProgress = items.filter(item => item.status_label === 'in_progress').length
      const pending = items.filter(item => 
        item.status_label === 'pending' || 
        (item.status_label === null && item.status === false)
      ).length

      return [
        { 
          name: 'Pending', 
          value: Math.round((pending / total) * 100), 
          count: pending,
          color: '#9ca3af' 
        },
        { 
          name: 'In Progress', 
          value: Math.round((inProgress / total) * 100), 
          count: inProgress,
          color: '#f59e0b' 
        },
        { 
          name: 'Selesai', 
          value: Math.round((completed / total) * 100), 
          count: completed,
          color: '#22c55e' 
        }
      ]
    } catch (error) {
      console.error('Error fetching status distribution:', error)
      throw error
    }
  }

  // Get monthly completion chart data
  static async getMonthlyCompletionChart(): Promise<MonthlyCompletionChart[]> {
    try {
      const twelveMonthsAgo = new Date()
      twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12)

      const { data: checklists, error } = await supabase
        .from('checklist_items')
        .select('created_at, completed_at')
        .gte('created_at', twelveMonthsAgo.toISOString())

      if (error) throw error

      const items = checklists || []
      
      // Initialize 12 months data
      const monthlyData: { [key: string]: { completed: number; created: number } } = {}
      
      for (let i = 11; i >= 0; i--) {
        const date = new Date()
        date.setMonth(date.getMonth() - i)
        const monthKey = date.toLocaleDateString('id-ID', { month: 'short', year: '2-digit' })
        monthlyData[monthKey] = { completed: 0, created: 0 }
      }

      // Count items by month
      items.forEach(item => {
        // Count created items
        const createdDate = new Date(item.created_at)
        const createdMonthKey = createdDate.toLocaleDateString('id-ID', { month: 'short', year: '2-digit' })
        if (monthlyData[createdMonthKey]) {
          monthlyData[createdMonthKey].created++
        }

        // Count completed items
        if (item.completed_at) {
          const completedDate = new Date(item.completed_at)
          const completedMonthKey = completedDate.toLocaleDateString('id-ID', { month: 'short', year: '2-digit' })
          if (monthlyData[completedMonthKey]) {
            monthlyData[completedMonthKey].completed++
          }
        }
      })

      return Object.entries(monthlyData).map(([month, data]) => ({
        month,
        completed: data.completed,
        created: data.created
      }))
    } catch (error) {
      console.error('Error fetching monthly completion chart:', error)
      throw error
    }
  }

  // Get priority distribution chart data
  static async getPriorityDistributionChart(): Promise<PriorityDistributionChart[]> {
    try {
      const { data: checklists, error } = await supabase
        .from('checklist_items')
        .select('priority')

      if (error) throw error

      const items = checklists || []
      const total = items.length

      if (total === 0) {
        return [
          { priority: 'High', count: 0, percentage: 0, color: '#ef4444' },
          { priority: 'Medium', count: 0, percentage: 0, color: '#f59e0b' },
          { priority: 'Low', count: 0, percentage: 0, color: '#22c55e' }
        ]
      }

      const high = items.filter(item => item.priority === 'high').length
      const medium = items.filter(item => item.priority === 'medium').length
      const low = items.filter(item => item.priority === 'low').length

      return [
        { 
          priority: 'High', 
          count: high, 
          percentage: Math.round((high / total) * 100),
          color: '#ef4444' 
        },
        { 
          priority: 'Medium', 
          count: medium, 
          percentage: Math.round((medium / total) * 100),
          color: '#f59e0b' 
        },
        { 
          priority: 'Low', 
          count: low, 
          percentage: Math.round((low / total) * 100),
          color: '#22c55e' 
        }
      ]
    } catch (error) {
      console.error('Error fetching priority distribution:', error)
      throw error
    }
  }

  // Get document type distribution
  static async getDocumentTypeChart(): Promise<DocumentTypeChart[]> {
    try {
      const { data: documents, error } = await supabase
        .from('documents')
        .select('file_name, file_size')

      if (error) throw error

      const items = documents || []
      const typeMap: { [key: string]: { count: number; size: number } } = {}

      items.forEach(doc => {
        const extension = doc.file_name.split('.').pop()?.toLowerCase() || 'unknown'
        const size = parseFloat(doc.file_size?.replace(' MB', '') || '0')
        
        if (!typeMap[extension]) {
          typeMap[extension] = { count: 0, size: 0 }
        }
        
        typeMap[extension].count++
        typeMap[extension].size += size
      })

      const colors = ['#3b82f6', '#ef4444', '#22c55e', '#f59e0b', '#8b5cf6', '#ec4899']
      
      return Object.entries(typeMap)
        .map(([type, data], index) => ({
          type: type.toUpperCase(),
          count: data.count,
          size: Math.round(data.size * 100) / 100,
          color: colors[index % colors.length]
        }))
        .sort((a, b) => b.count - a.count)
    } catch (error) {
      console.error('Error fetching document type chart:', error)
      throw error
    }
  }

  // Export data for CSV/Excel
  static async exportProjectData(format: 'csv' | 'json' = 'csv'): Promise<string> {
    try {
      const projectReports = await this.getProjectReports()
      
      if (format === 'json') {
        return JSON.stringify(projectReports, null, 2)
      }

      // CSV format
      const headers = [
        'Project Name',
        'Status', 
        'Total Checklist',
        'Completed',
        'In Progress',
        'Pending',
        'Progress %',
        'Total Documents',
        'Created Date'
      ]

      const rows = projectReports.map(project => [
        project.name,
        project.status,
        project.totalChecklist.toString(),
        project.completedChecklist.toString(),
        project.inProgressChecklist.toString(),
        project.pendingChecklist.toString(),
        project.progress.toString(),
        project.documents.length.toString(),
        new Date(project.created_at).toLocaleDateString('id-ID')
      ])

      const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n')

      return csvContent
    } catch (error) {
      console.error('Error exporting project data:', error)
      throw error
    }
  }

  // Format date for display
  static formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Format file size
  static formatFileSize(sizeString: string): string {
    const size = parseFloat(sizeString.replace(' MB', ''))
    if (size < 1) {
      return `${Math.round(size * 1024)} KB`
    }
    return `${size} MB`
  }

  // Get project status badge variant
  static getStatusBadgeVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
    switch (status) {
      case 'active':
        return 'default'
      case 'completed':
        return 'secondary'
      case 'paused':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  // Get priority badge variant
  static getPriorityBadgeVariant(priority: string): "default" | "secondary" | "destructive" | "outline" {
    switch (priority) {
      case 'high':
        return 'destructive'
      case 'medium':
        return 'default'
      case 'low':
        return 'secondary'
      default:
        return 'outline'
    }
  }
} 