import { supabase } from '@/lib/supabase'
import type { ChecklistItem, ChecklistStatus } from '@/lib/supabase'

export interface ChecklistItemWithDetails extends ChecklistItem {
  project_name: string
  project_id: string
  indicator_name: string
}

export interface ChecklistStats {
  total: number
  completed: number
  in_progress: number
  pending: number
}

export interface ChecklistFilters {
  searchTerm?: string
  statusFilter?: 'all' | 'completed' | 'in_progress' | 'pending'
  priorityFilter?: 'all' | 'high' | 'medium' | 'low'
  projectFilter?: 'all' | string
}

export class ChecklistService {
  // Get all checklist items with project and indicator details
  static async getAllChecklistItems(): Promise<ChecklistItemWithDetails[]> {
    try {
      const { data, error } = await supabase
        .from('checklist_items')
        .select(`
          *,
          indicators!inner(
            name,
            project_id,
            projects!inner(
              name
            )
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      return (data || []).map(item => ({
        ...item,
        project_name: item.indicators.projects.name,
        project_id: item.indicators.project_id,
        indicator_name: item.indicators.name
      }))
    } catch (error) {
      console.error('Error fetching checklist items:', error)
      throw error
    }
  }

  // Get filtered checklist items
  static async getFilteredChecklistItems(filters: ChecklistFilters): Promise<ChecklistItemWithDetails[]> {
    try {
      let query = supabase
        .from('checklist_items')
        .select(`
          *,
          indicators!inner(
            name,
            project_id,
            projects!inner(
              name
            )
          )
        `)

      // Apply project filter
      if (filters.projectFilter && filters.projectFilter !== 'all') {
        query = query.eq('indicators.project_id', filters.projectFilter)
      }

      // Apply status filter
      if (filters.statusFilter && filters.statusFilter !== 'all') {
        if (filters.statusFilter === 'completed') {
          query = query.eq('status', true)
        } else if (filters.statusFilter === 'pending') {
          query = query.or('status_label.eq.pending,status_label.is.null').eq('status', false)
        } else if (filters.statusFilter === 'in_progress') {
          query = query.eq('status_label', 'in_progress')
        }
      }

      // Apply priority filter
      if (filters.priorityFilter && filters.priorityFilter !== 'all') {
        query = query.eq('priority', filters.priorityFilter)
      }

      query = query.order('created_at', { ascending: false })

      const { data, error } = await query

      if (error) throw error

      let filteredData = (data || []).map(item => ({
        ...item,
        project_name: item.indicators.projects.name,
        project_id: item.indicators.project_id,
        indicator_name: item.indicators.name
      }))

      // Apply search filter (client-side for better UX)
      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase()
        filteredData = filteredData.filter(item =>
          item.description.toLowerCase().includes(searchLower) ||
          item.project_name.toLowerCase().includes(searchLower) ||
          item.indicator_name.toLowerCase().includes(searchLower)
        )
      }

      return filteredData
    } catch (error) {
      console.error('Error fetching filtered checklist items:', error)
      throw error
    }
  }

  // Get checklist statistics
  static async getChecklistStats(): Promise<ChecklistStats> {
    try {
      const { data, error } = await supabase
        .from('checklist_items')
        .select('status, status_label')

      if (error) throw error

      const items = data || []
      const total = items.length
      const completed = items.filter(item => item.status === true).length
      const in_progress = items.filter(item => item.status_label === 'in_progress').length
      const pending = items.filter(item => 
        item.status_label === 'pending' || 
        (item.status_label === null && item.status === false)
      ).length

      return {
        total,
        completed,
        in_progress,
        pending
      }
    } catch (error) {
      console.error('Error fetching checklist stats:', error)
      throw error
    }
  }

  // Update checklist status
  static async updateChecklistStatus(id: string, statusLabel: ChecklistStatus): Promise<ChecklistItem> {
    try {
      const updates = {
        status_label: statusLabel,
        status: statusLabel === 'completed',
        completed_at: statusLabel === 'completed' ? new Date().toISOString() : null
      }

      const { data, error } = await supabase
        .from('checklist_items')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating checklist status:', error)
      throw error
    }
  }

  // Toggle checklist status (backward compatibility)
  static async toggleChecklistStatus(id: string): Promise<ChecklistItem> {
    try {
      // Get current status
      const { data: currentData, error: fetchError } = await supabase
        .from('checklist_items')
        .select('status, status_label')
        .eq('id', id)
        .single()

      if (fetchError) throw fetchError

      // Determine next status
      let nextStatusLabel: ChecklistStatus
      if (currentData.status_label === 'pending' || currentData.status_label === null) {
        nextStatusLabel = 'completed'
      } else if (currentData.status_label === 'in_progress') {
        nextStatusLabel = 'completed'
      } else {
        nextStatusLabel = 'pending'
      }

      return this.updateChecklistStatus(id, nextStatusLabel)
    } catch (error) {
      console.error('Error toggling checklist status:', error)
      throw error
    }
  }

  // Get status label for display
  static getStatusLabel(statusLabel: string | null, status: boolean): string {
    const currentStatus = statusLabel || (status ? 'completed' : 'pending')
    
    switch (currentStatus) {
      case 'completed':
        return 'Selesai'
      case 'in_progress':
        return 'Progress'
      case 'pending':
      default:
        return 'Pending'
    }
  }

  // Get status color
  static getStatusColor(statusLabel: string | null, status: boolean): string {
    const currentStatus = statusLabel || (status ? 'completed' : 'pending')
    
    switch (currentStatus) {
      case 'completed':
        return 'bg-green-500'
      case 'in_progress':
        return 'bg-yellow-500'
      case 'pending':
      default:
        return 'bg-gray-400'
    }
  }

  // Get priority label
  static getPriorityLabel(priority: string): string {
    switch (priority) {
      case 'high':
        return 'Tinggi'
      case 'medium':
        return 'Sedang'
      case 'low':
        return 'Rendah'
      default:
        return 'Sedang'
    }
  }

  // Get priority color
  static getPriorityColor(priority: string): string {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800'
      case 'low':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }
} 