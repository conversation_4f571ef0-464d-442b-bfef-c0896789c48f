import { supabase } from '@/lib/supabase'
import type { Document, DocumentInsert, DocumentUpdate } from '@/lib/supabase'

export interface DocumentWithProject extends Document {
  project_name?: string
}

export class DocumentService {
  // Get all documents with project info
  static async getAllDocuments(): Promise<DocumentWithProject[]> {
    try {
      const { data, error } = await supabase
        .from('documents')
        .select(`
          *,
          projects!inner(name)
        `)
        .order('uploaded_at', { ascending: false })

      if (error) throw error

      return (data || []).map(doc => ({
        ...doc,
        project_name: doc.projects?.name
      }))
    } catch (error) {
      console.error('Error fetching documents:', error)
      throw error
    }
  }

  // Get documents by project ID
  static async getDocumentsByProjectId(projectId: string): Promise<Document[]> {
    try {
      const { data, error } = await supabase
        .from('documents')
        .select('*')
        .eq('project_id', projectId)
        .order('uploaded_at', { ascending: false })

      if (error) throw error
      return data || []
    } catch (error) {
      console.error('Error fetching documents by project:', error)
      throw error
    }
  }

  // Upload file to Supabase Storage
  static async uploadFile(file: File, projectId: string): Promise<string> {
    try {
      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
      const filePath = `${projectId}/${fileName}`

      const { data, error } = await supabase.storage
        .from('documents')
        .upload(filePath, file)

      if (error) throw error

      const { data: { publicUrl } } = supabase.storage
        .from('documents')
        .getPublicUrl(filePath)

      return publicUrl
    } catch (error) {
      console.error('Error uploading file:', error)
      throw error
    }
  }

  // Create document record
  static async createDocument(documentData: { project_id: string; file: File; uploaded_by?: string }): Promise<Document> {
    try {
      // First upload the file
      const fileUrl = await this.uploadFile(documentData.file, documentData.project_id)

      // Create document record
      const docRecord: DocumentInsert = {
        project_id: documentData.project_id,
        file_name: documentData.file.name,
        file_url: fileUrl,
        file_size: `${(documentData.file.size / (1024 * 1024)).toFixed(2)} MB`,
        file_type: documentData.file.type || 'application/octet-stream',
        uploaded_by: documentData.uploaded_by || 'Current User',
      }

      const { data, error } = await supabase
        .from('documents')
        .insert([docRecord])
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error creating document:', error)
      throw error
    }
  }

  // Update document
  static async updateDocument(id: string, updates: DocumentUpdate): Promise<Document> {
    try {
      const { data, error } = await supabase
        .from('documents')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error updating document:', error)
      throw error
    }
  }

  // Delete document
  static async deleteDocument(id: string): Promise<void> {
    try {
      // First get the document to get file path
      const { data: doc, error: fetchError } = await supabase
        .from('documents')
        .select('file_url, project_id')
        .eq('id', id)
        .single()

      if (fetchError) throw fetchError

      // Extract file path from URL for deletion
      if (doc.file_url) {
        const urlParts = doc.file_url.split('/')
        const fileName = urlParts[urlParts.length - 1]
        const filePath = `${doc.project_id}/${fileName}`

        // Delete from storage
        await supabase.storage
          .from('documents')
          .remove([filePath])
      }

      // Delete from database
      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('id', id)

      if (error) throw error
    } catch (error) {
      console.error('Error deleting document:', error)
      throw error
    }
  }

  // Get file download URL
  static async getDownloadUrl(filePath: string): Promise<string> {
    try {
      const { data, error } = await supabase.storage
        .from('documents')
        .createSignedUrl(filePath, 3600) // 1 hour expiry

      if (error) throw error
      return data.signedUrl
    } catch (error) {
      console.error('Error getting download URL:', error)
      throw error
    }
  }

  // Get file type icon and color
  static getFileTypeInfo(fileName: string): { icon: string; color: string; type: string } {
    const ext = fileName.split('.').pop()?.toLowerCase() || ''
    
    const typeMap: Record<string, { icon: string; color: string; type: string }> = {
      pdf: { icon: '📄', color: 'bg-red-100 text-red-800', type: 'PDF' },
      doc: { icon: '📝', color: 'bg-blue-100 text-blue-800', type: 'Word' },
      docx: { icon: '📝', color: 'bg-blue-100 text-blue-800', type: 'Word' },
      xls: { icon: '📊', color: 'bg-green-100 text-green-800', type: 'Excel' },
      xlsx: { icon: '📊', color: 'bg-green-100 text-green-800', type: 'Excel' },
      ppt: { icon: '📽️', color: 'bg-orange-100 text-orange-800', type: 'PowerPoint' },
      pptx: { icon: '📽️', color: 'bg-orange-100 text-orange-800', type: 'PowerPoint' },
      txt: { icon: '📄', color: 'bg-gray-100 text-gray-800', type: 'Text' },
      jpg: { icon: '🖼️', color: 'bg-purple-100 text-purple-800', type: 'Image' },
      jpeg: { icon: '🖼️', color: 'bg-purple-100 text-purple-800', type: 'Image' },
      png: { icon: '🖼️', color: 'bg-purple-100 text-purple-800', type: 'Image' },
      gif: { icon: '🖼️', color: 'bg-purple-100 text-purple-800', type: 'Image' },
      zip: { icon: '🗜️', color: 'bg-yellow-100 text-yellow-800', type: 'Archive' },
      rar: { icon: '🗜️', color: 'bg-yellow-100 text-yellow-800', type: 'Archive' },
    }

    return typeMap[ext] || { icon: '📁', color: 'bg-gray-100 text-gray-800', type: 'File' }
  }
} 