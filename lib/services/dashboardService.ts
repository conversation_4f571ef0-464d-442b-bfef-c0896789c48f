import { supabase } from '@/lib/supabase'
import type { Project } from '@/lib/supabase'

export interface DashboardStats {
  totalProjects: number
  activeProjects: number
  totalChecklists: number
  completedChecklists: number
  overallProgress: number
  totalDocuments: number
  totalStorageSize: number
}

export interface ProjectStats extends Project {
  totalChecklist: number
  completedChecklist: number
  progress: number
}

export interface StatusDistribution {
  name: string
  value: number
  color: string
}

export interface MonthlyProgress {
  name: string
  completed: number
  pending: number
  in_progress: number
}

export interface RecentActivity {
  id: string
  type: 'checklist' | 'document' | 'project'
  title: string
  description: string
  timestamp: string
  projectName?: string
}

export class DashboardService {
  // Get overall dashboard statistics
  static async getDashboardStats(): Promise<DashboardStats> {
    try {
      // Get all projects with their checklist data
      const { data: projects, error: projectsError } = await supabase
        .from('projects')
        .select(`
          id,
          status,
          indicators (
            id,
            checklist_items (
              id,
              status,
              status_label
            )
          )
        `)

      if (projectsError) throw projectsError

      // Get documents count and size
      const { data: documents, error: documentsError } = await supabase
        .from('documents')
        .select('file_size')

      if (documentsError) throw documentsError

      // Calculate statistics
      const totalProjects = projects?.length || 0
      const activeProjects = projects?.filter(p => p.status === 'active').length || 0
      
      let totalChecklists = 0
      let completedChecklists = 0

      projects?.forEach(project => {
        project.indicators?.forEach((indicator: any) => {
          if (indicator.checklist_items) {
            totalChecklists += indicator.checklist_items.length
            completedChecklists += indicator.checklist_items.filter((item: any) => item.status === true).length
          }
        })
      })

      const overallProgress = totalChecklists > 0 ? Math.round((completedChecklists / totalChecklists) * 100) : 0
      const totalDocuments = documents?.length || 0
      
      // Calculate total storage size
      const totalStorageSize = documents?.reduce((total, doc) => {
        const size = parseFloat(doc.file_size?.replace(' MB', '') || '0')
        return total + size
      }, 0) || 0

      return {
        totalProjects,
        activeProjects,
        totalChecklists,
        completedChecklists,
        overallProgress,
        totalDocuments,
        totalStorageSize: Math.round(totalStorageSize * 100) / 100
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      throw error
    }
  }

  // Get projects with their progress
  static async getProjectsWithProgress(): Promise<ProjectStats[]> {
    try {
      const { data: projects, error } = await supabase
        .from('projects')
        .select(`
          *,
          indicators (
            id,
            checklist_items (
              id,
              status
            )
          )
        `)
        .order('updated_at', { ascending: false })
        .limit(10)

      if (error) throw error

      return (projects || []).map(project => {
        let totalChecklist = 0
        let completedChecklist = 0

        project.indicators?.forEach((indicator: any) => {
          if (indicator.checklist_items) {
            totalChecklist += indicator.checklist_items.length
            completedChecklist += indicator.checklist_items.filter((item: any) => item.status === true).length
          }
        })

        const progress = totalChecklist > 0 ? Math.round((completedChecklist / totalChecklist) * 100) : 0

        return {
          ...project,
          totalChecklist,
          completedChecklist,
          progress
        }
      })
    } catch (error) {
      console.error('Error fetching projects with progress:', error)
      throw error
    }
  }

  // Get status distribution for pie chart
  static async getStatusDistribution(): Promise<StatusDistribution[]> {
    try {
      const { data: checklists, error } = await supabase
        .from('checklist_items')
        .select('status, status_label')

      if (error) throw error

      const items = checklists || []
      const total = items.length

      if (total === 0) {
        return [
          { name: 'Pending', value: 0, color: '#9ca3af' },
          { name: 'In Progress', value: 0, color: '#f59e0b' },
          { name: 'Selesai', value: 0, color: '#22c55e' }
        ]
      }

      const completed = items.filter(item => item.status === true).length
      const inProgress = items.filter(item => item.status_label === 'in_progress').length
      const pending = items.filter(item => 
        item.status_label === 'pending' || 
        (item.status_label === null && item.status === false)
      ).length

      return [
        { 
          name: 'Pending', 
          value: Math.round((pending / total) * 100), 
          color: '#9ca3af' 
        },
        { 
          name: 'In Progress', 
          value: Math.round((inProgress / total) * 100), 
          color: '#f59e0b' 
        },
        { 
          name: 'Selesai', 
          value: Math.round((completed / total) * 100), 
          color: '#22c55e' 
        }
      ]
    } catch (error) {
      console.error('Error fetching status distribution:', error)
      throw error
    }
  }

  // Get monthly progress data for bar chart
  static async getMonthlyProgress(): Promise<MonthlyProgress[]> {
    try {
      // Get checklist items created in the last 6 months
      const sixMonthsAgo = new Date()
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6)

      const { data: checklists, error } = await supabase
        .from('checklist_items')
        .select('status, status_label, completed_at, created_at')
        .gte('created_at', sixMonthsAgo.toISOString())

      if (error) throw error

      const items = checklists || []
      
      // Group by month
      const monthlyData: { [key: string]: { completed: number; pending: number; in_progress: number } } = {}
      
      // Initialize last 6 months
      for (let i = 5; i >= 0; i--) {
        const date = new Date()
        date.setMonth(date.getMonth() - i)
        const monthKey = date.toLocaleDateString('id-ID', { month: 'short' })
        monthlyData[monthKey] = { completed: 0, pending: 0, in_progress: 0 }
      }

      // Count items by month and status
      items.forEach(item => {
        const itemDate = new Date(item.created_at)
        const monthKey = itemDate.toLocaleDateString('id-ID', { month: 'short' })
        
        if (monthlyData[monthKey]) {
          if (item.status === true) {
            monthlyData[monthKey].completed++
          } else if (item.status_label === 'in_progress') {
            monthlyData[monthKey].in_progress++
          } else {
            monthlyData[monthKey].pending++
          }
        }
      })

      return Object.entries(monthlyData).map(([name, data]) => ({
        name,
        ...data
      }))
    } catch (error) {
      console.error('Error fetching monthly progress:', error)
      throw error
    }
  }

  // Get recent activities
  static async getRecentActivities(): Promise<RecentActivity[]> {
    try {
      const activities: RecentActivity[] = []

      // Get recent checklist completions
      const { data: recentChecklists, error: checklistError } = await supabase
        .from('checklist_items')
        .select(`
          id,
          description,
          completed_at,
          indicators (
            name,
            projects (
              name
            )
          )
        `)
        .not('completed_at', 'is', null)
        .order('completed_at', { ascending: false })
        .limit(5)

      if (!checklistError && recentChecklists) {
        recentChecklists.forEach(item => {
          activities.push({
            id: `checklist-${item.id}`,
            type: 'checklist',
            title: 'Checklist Selesai',
            description: item.description,
            timestamp: item.completed_at!,
            projectName: (item.indicators as any)?.projects?.name
          })
        })
      }

      // Get recent documents
      const { data: recentDocuments, error: documentError } = await supabase
        .from('documents')
        .select(`
          id,
          file_name,
          uploaded_at,
          projects (
            name
          )
        `)
        .order('uploaded_at', { ascending: false })
        .limit(5)

      if (!documentError && recentDocuments) {
        recentDocuments.forEach(doc => {
          activities.push({
            id: `document-${doc.id}`,
            type: 'document',
            title: 'Dokumen Diupload',
            description: doc.file_name,
            timestamp: doc.uploaded_at,
            projectName: (doc.projects as any)?.name
          })
        })
      }

      // Get recent projects
      const { data: recentProjects, error: projectError } = await supabase
        .from('projects')
        .select('id, name, created_at')
        .order('created_at', { ascending: false })
        .limit(3)

      if (!projectError && recentProjects) {
        recentProjects.forEach(project => {
          activities.push({
            id: `project-${project.id}`,
            type: 'project',
            title: 'Project Baru',
            description: project.name,
            timestamp: project.created_at
          })
        })
      }

      // Sort all activities by timestamp and return top 10
      return activities
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 10)
    } catch (error) {
      console.error('Error fetching recent activities:', error)
      throw error
    }
  }

  // Get project priority distribution
  static async getPriorityDistribution(): Promise<{ high: number; medium: number; low: number }> {
    try {
      const { data: checklists, error } = await supabase
        .from('checklist_items')
        .select('priority')

      if (error) throw error

      const items = checklists || []
      const high = items.filter(item => item.priority === 'high').length
      const medium = items.filter(item => item.priority === 'medium').length
      const low = items.filter(item => item.priority === 'low').length

      return { high, medium, low }
    } catch (error) {
      console.error('Error fetching priority distribution:', error)
      throw error
    }
  }

  // Format date for display
  static formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get activity icon
  static getActivityIcon(type: 'checklist' | 'document' | 'project'): string {
    switch (type) {
      case 'checklist':
        return '✅'
      case 'document':
        return '📄'
      case 'project':
        return '📁'
      default:
        return '📋'
    }
  }
} 