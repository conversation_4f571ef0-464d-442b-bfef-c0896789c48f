# Reports & Analytics - Real Data Implementation

## ✅ Fitur yang Diimplementasi

### 📊 **Lapor<PERSON> Komprehensif**
- **Summary Statistics**: Total projects, checklists, progress rata-rata, storage
- **Project Filtering**: Filter berdasarkan project tertentu atau semua
- **Report Types**: Summary, Detailed, Analytics views
- **Real-time Data**: Data langsung dari Supabase dengan refresh

### 📈 **Visual Analytics**
- **Status Distribution**: Pie chart distribusi status checklist
- **Project Progress**: Horizontal bar chart progress per project
- **Monthly Trends**: Area chart tren completion 12 bulan
- **Priority Analysis**: Bar chart distribusi prioritas
- **Document Analytics**: Dual-axis chart tipe dokumen

### 📋 **Detailed Reporting**
- **Project Breakdown**: Detail per project dengan indicators
- **Performance Metrics**: Completion rates dan progress tracking
- **Document Management**: File count dan storage analysis
- **Export Functionality**: CSV export untuk external analysis

### 🔄 **Export & Print**
- **CSV Export**: Export data ke spreadsheet
- **Print Report**: Print-friendly formatting
- **PDF Export**: (Planned) Export ke PDF
- **Data Refresh**: Manual refresh untuk data terbaru

## 🏗️ **Arsitektur Reports**

### ReportService
```typescript
class ReportService {
  // Data comprehensive untuk reports
  static getProjectReports(projectId?: number): Promise<ProjectReportData[]>
  
  // Summary statistics aggregation
  static getReportSummary(): Promise<ReportSummary>
  
  // Chart data untuk analytics
  static getProjectProgressChart(): Promise<ProjectProgressChart[]>
  static getStatusDistributionChart(): Promise<StatusDistributionChart[]>
  static getMonthlyCompletionChart(): Promise<MonthlyCompletionChart[]>
  static getPriorityDistributionChart(): Promise<PriorityDistributionChart[]>
  static getDocumentTypeChart(): Promise<DocumentTypeChart[]>
  
  // Export functionality
  static exportProjectData(format: 'csv' | 'json'): Promise<string>
}
```

### Data Types
```typescript
interface ProjectReportData {
  id: number
  name: string
  status: string
  totalChecklist: number
  completedChecklist: number
  inProgressChecklist: number
  pendingChecklist: number
  progress: number
  indicators: IndicatorReport[]
  documents: DocumentReport[]
}

interface ReportSummary {
  totalProjects: number
  activeProjects: number
  completedProjects: number
  totalChecklists: number
  completedChecklists: number
  averageProgress: number
  totalDocuments: number
  totalStorageSize: number
}
```

## 📊 **Data Aggregation Strategy**

### Complex Joins & Calculations
1. **Project Reports**: JOIN projects → indicators → checklist_items + documents
2. **Status Aggregation**: Group checklist by status_label dengan fallback logic
3. **Progress Calculation**: Weighted average berdasarkan total checklist
4. **Time-based Analysis**: Monthly grouping dengan 12 bulan rolling window

### Performance Optimization
- **Parallel Queries**: Promise.all() untuk multiple chart data
- **Selective Fields**: Only fetch necessary columns untuk reduce bandwidth
- **Client-side Filtering**: Filter project di client untuk responsiveness
- **Computed Fields**: Calculate progress, percentages di service layer

## 🎨 **UI Organization**

### Tab-based Layout
```jsx
<Tabs defaultValue="overview">
  <TabsList>
    <TabsTrigger value="overview">Overview</TabsTrigger>
    <TabsTrigger value="analytics">Analytics</TabsTrigger>
    <TabsTrigger value="projects">Projects</TabsTrigger>
  </TabsList>
</Tabs>
```

### Chart Components
- **Overview Tab**: Status pie chart + Project progress horizontal bar
- **Analytics Tab**: Monthly trend area chart + Priority/Document analysis
- **Projects Tab**: Detailed breakdown dengan indicators dan documents

### Filter Controls
- **Project Selection**: Dropdown semua project atau specific project
- **Report Type**: Summary/Detailed/Analytics view options
- **Date Range**: (Future) Time-based filtering

## 📈 **Chart Implementations**

### Status Distribution Pie Chart
```jsx
<PieChart>
  <Pie
    data={statusDistribution}
    innerRadius={60}
    outerRadius={120}
    paddingAngle={5}
    dataKey="value"
  >
    {data.map((entry, index) => (
      <Cell key={index} fill={entry.color} />
    ))}
  </Pie>
  <Tooltip formatter={(value, name, props) => [
    `${value}% (${props.payload.count} item)`,
    name
  ]} />
</PieChart>
```

### Project Progress Horizontal Bar
```jsx
<BarChart data={projectProgressChart} layout="horizontal">
  <XAxis type="number" />
  <YAxis dataKey="name" type="category" width={80} />
  <Bar dataKey="completed" stackId="a" fill="#22c55e" name="Selesai" />
  <Bar dataKey="inProgress" stackId="a" fill="#f59e0b" name="Progress" />
  <Bar dataKey="pending" stackId="a" fill="#9ca3af" name="Pending" />
</BarChart>
```

### Monthly Completion Area Chart
```jsx
<AreaChart data={monthlyCompletion}>
  <Area 
    type="monotone" 
    dataKey="created" 
    stackId="1" 
    stroke="#3b82f6" 
    fill="#3b82f6" 
    name="Dibuat"
    fillOpacity={0.6}
  />
  <Area 
    type="monotone" 
    dataKey="completed" 
    stackId="2" 
    stroke="#22c55e" 
    fill="#22c55e" 
    name="Selesai"
    fillOpacity={0.8}
  />
</AreaChart>
```

### Document Type Dual-Axis Chart
```jsx
<BarChart data={documentTypes}>
  <YAxis yAxisId="left" />
  <YAxis yAxisId="right" orientation="right" />
  <Bar yAxisId="left" dataKey="count" fill="#3b82f6" name="Jumlah File" />
  <Bar yAxisId="right" dataKey="size" fill="#22c55e" name="Size (MB)" />
</BarChart>
```

## 📊 **Analytics Features**

### Summary Statistics
- **Project Overview**: Total, active, completed projects
- **Checklist Metrics**: Total, completed, progress percentage
- **Storage Analytics**: Document count, total size MB
- **Completion Rate**: Average progress across all projects

### Trend Analysis
- **12-Month Window**: Rolling 12 bulan untuk historical analysis
- **Created vs Completed**: Perbandingan checklist dibuat vs selesai
- **Monthly Patterns**: Identify peak productivity periods
- **Growth Tracking**: Monitor project completion trends

### Priority & Document Analytics
- **Priority Distribution**: High/Medium/Low priority breakdown
- **Document Types**: File extension analysis dengan size tracking
- **Storage Usage**: Monitor storage consumption per file type
- **File Management**: Track document upload patterns

## 🔄 **Export & Sharing**

### CSV Export Implementation
```typescript
static async exportProjectData(format: 'csv' | 'json' = 'csv'): Promise<string> {
  const projectReports = await this.getProjectReports()
  
  const headers = [
    'Project Name', 'Status', 'Total Checklist',
    'Completed', 'In Progress', 'Pending', 
    'Progress %', 'Total Documents', 'Created Date'
  ]
  
  const rows = projectReports.map(project => [
    project.name, project.status,
    project.totalChecklist.toString(),
    // ... other fields
  ])
  
  return [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n')
}
```

### Export Features
- **CSV Download**: Client-side blob creation untuk download
- **Print Optimization**: CSS print styles untuk clean printing
- **Data Formatting**: Proper date formatting untuk Indonesian locale
- **File Naming**: Timestamped filename untuk organization

## 📱 **Responsive Design**

### Layout Adaptation
- **Desktop**: 2-column charts, side-by-side analytics
- **Tablet**: Single column dengan maintained chart sizes
- **Mobile**: Stacked layout dengan horizontal scroll charts

### Chart Responsiveness
- **ResponsiveContainer**: Auto-resize berdasarkan container width
- **Fixed Heights**: Consistent 300px height untuk all charts
- **Horizontal Charts**: Better mobile experience untuk project progress
- **Tooltip Optimization**: Mobile-friendly tooltip positioning

## 🔍 **Detailed Project Reports**

### Project Breakdown
```jsx
{filteredData.map((project) => (
  <div key={project.id} className="border rounded-lg p-6">
    {/* Header dengan status dan progress */}
    <div className="flex items-center justify-between mb-4">
      <div>
        <h3 className="text-lg font-semibold">{project.name}</h3>
        <p className="text-sm text-muted-foreground">
          Dibuat: {ReportService.formatDate(project.created_at)}
        </p>
      </div>
      <Badge variant={ReportService.getStatusBadgeVariant(project.status)}>
        {project.status}
      </Badge>
    </div>

    {/* Statistics grid */}
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      {/* Total, Completed, In Progress, Pending */}
    </div>

    {/* Indicators breakdown */}
    {project.indicators.map((indicator) => (
      <div className="flex items-center justify-between p-3 border rounded">
        <div className="font-medium">{indicator.name}</div>
        <Progress value={indicator.progress} className="w-20" />
      </div>
    ))}

    {/* Documents list */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
      {project.documents.map((doc) => (
        <div className="flex items-center space-x-2 p-2 border rounded">
          <FileText className="h-4 w-4" />
          <div className="truncate">{doc.file_name}</div>
        </div>
      ))}
    </div>
  </div>
))}
```

## 🎯 **Business Intelligence**

### For Management
- **Executive Dashboard**: High-level metrics dan trends
- **Performance Tracking**: Project completion rates
- **Resource Planning**: Storage usage dan document management
- **ROI Analysis**: Progress vs timeline tracking

### For Project Managers
- **Project Deep-dive**: Individual project performance
- **Team Productivity**: Checklist completion patterns
- **Bottleneck Identification**: Pending vs in-progress analysis
- **Timeline Management**: Monthly completion trends

### For Team Members
- **Progress Visibility**: Clear progress indicators
- **Priority Focus**: Priority distribution untuk task planning
- **Document Access**: File management dan organization
- **Historical Context**: Past performance untuk improvement

Reports sekarang menyediakan **business intelligence** yang komprehensif dengan data real dari Supabase! 📊 