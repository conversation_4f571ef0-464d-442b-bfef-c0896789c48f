# Google Token Storage System

## Situasi Saat Ini

Ketika Anda memasukkan Access Token dan Refresh Token melalui UI di halaman `/settings/google-tokens`, berikut yang terjadi:

### 1. **Penyimpanan Token**

#### **Sebelum Update (Sistem Lama):**
- Token hanya disimpan di file `.env.local`
- Harus manual update environment variables
- Tidak ada penyimpanan otomatis

#### **Setelah Update (Sistem Baru):**
- ✅ Token disimpan otomatis ke **database Supabase**
- ✅ Validasi token dengan Google API
- ✅ Tracking expiration time
- ✅ Support untuk multiple token history
- ✅ Fallback ke environment variables jika database kosong

### 2. **Alur Penyimpanan Token**

```
1. User input token di UI → 
2. API validates token dengan Google → 
3. Token disimpan ke database Supabase → 
4. Environment variables sebagai fallback
```

### 3. **Database Schema**

Tabel `google_tokens` telah ditambahkan dengan struktur:

```sql
CREATE TABLE google_tokens (
    id UUID PRIMARY KEY,
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at TIMESTAMP,
    scopes TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

### 4. **Setup yang Diperlukan**

#### **A. Jalankan SQL Script untuk Database:**

```bash
# Buka Supabase Dashboard → SQL Editor
# Jalankan file: scripts/google-tokens-table.sql
```

#### **B. Update .env.local (Tambahkan Refresh Token):**

```env
NEXT_PUBLIC_SUPABASE_URL=https://explore-spb-checklist.poe7ey.easypanel.host/
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

NEXT_PUBLIC_GOOGLE_API_KEY=AIzaSyDn-BIq3tpyi3q0miHYqeeThcmFhRGT7Fc
NEXT_PUBLIC_GOOGLE_OAUTH_TOKEN=***********************************************...
NEXT_PUBLIC_GOOGLE_REFRESH_TOKEN=1//04... # TAMBAHKAN INI

# Google OAuth Credentials (untuk refresh token)
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret

NEXT_PUBLIC_MASTER_SPREADSHEET_ID=1amIuhtLOjHKQvDDkJpd2UEaDNkzCAPGvVaJAbCtxRcY
NEXT_PUBLIC_PROJECT_TEMPLATE_ID=1k3WsgNI3xVUjXOtYfrKAq-VxnXBeUvQ2vmWI8FkH3iY
NEXT_PUBLIC_PROJECTS_FOLDER_ID=18EK79cDzMW6zw_amknoir92_gcTkF3gw
```

### 5. **Cara Menggunakan**

#### **A. Input Token Baru:**
1. Buka `/settings/google-tokens`
2. Masukkan Access Token dan Refresh Token
3. Klik "Update Tokens"
4. ✅ Token otomatis tersimpan ke database

#### **B. Refresh Token:**
1. Klik tombol "Refresh Token" di UI
2. ✅ System otomatis refresh dan simpan token baru

#### **C. Check Status:**
1. Status token ditampilkan real-time
2. Menunjukkan sumber token (database/environment)
3. Expiration time tracking

### 6. **Keamanan**

- ✅ Token disimpan encrypted di database Supabase
- ✅ Row Level Security (RLS) enabled
- ✅ Token tidak di-expose di response API
- ✅ Automatic cleanup old tokens

### 7. **Fallback System**

Sistem akan mencari token dengan prioritas:
1. **Database** (primary) - Token aktif terbaru
2. **Environment Variables** (fallback) - Jika database kosong
3. **Error** - Jika tidak ada token tersedia

### 8. **API Endpoints**

- `POST /api/google-tokens/update` - Simpan token baru
- `GET /api/google-tokens/status` - Check status token
- `POST /api/google-tokens/refresh` - Refresh token

### 9. **Troubleshooting**

#### **Jika Token Tidak Tersimpan:**
1. Pastikan database table sudah dibuat
2. Check Supabase connection
3. Verify RLS policies

#### **Jika Refresh Gagal:**
1. Pastikan `GOOGLE_CLIENT_ID` dan `GOOGLE_CLIENT_SECRET` sudah diset
2. Check refresh token validity
3. Verify Google OAuth configuration

### 10. **Next Steps**

Untuk menyelesaikan implementasi:
1. ✅ Jalankan SQL script untuk database
2. ✅ Update .env.local dengan refresh token
3. ✅ Test input token di UI
4. 🔄 Fix Google Sheets Service integration (in progress)

## Status Implementation

- ✅ Database schema created
- ✅ Token service implemented  
- ✅ API routes updated
- ✅ UI integration working
- 🔄 Google Sheets Service integration (needs fixing)
- ⏳ Testing and validation
