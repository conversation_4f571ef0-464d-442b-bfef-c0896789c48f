# Integrasi Google Sheets - Checklist Management System

## Overview

Sistem ini mengintegrasikan Next.js + Supabase dengan Google Sheets untuk memungkinkan kolaborasi tim dalam mengelola checklist. Tim dapat mengedit status checklist langsung di Google Sheets, dan perubahan akan otomatis disinkronkan ke database Supabase.

## Workflow Integrasi

### 🟢 1. Pembuatan Project Baru
- User membuat project baru di aplikasi Next.js
- Data project disimpan ke Supabase
- Secara otomatis menambahkan project ke Google Sheets master
- Membuat Google Sheet baru untuk project tersebut dari template

### 🟢 2. Ko<PERSON>borasi di Google Sheets
- Tim menggunakan Google Sheets untuk mengedit status checklist
- Setiap perubahan status otomatis memicu Google Apps Script
- Status disinkronkan real-time ke Supabase

### 🟢 3. Sinkronisasi Data
- Perubahan di Google Sheets → Supabase (via Google Apps Script)
- Perubahan di aplikasi → Google Sheets (via API)
- Manual sync tersedia untuk sinkronisasi ulang

## Setup dan Konfigurasi

### 1. Google Cloud Console Setup

#### a. Aktifkan Google Sheets API
1. Buka [Google Cloud Console](https://console.cloud.google.com/)
2. Buat project baru atau pilih existing project
3. Navigate ke "APIs & Services" > "Library"
4. Cari dan aktifkan:
   - Google Sheets API
   - Google Drive API

#### b. Buat Service Account (Opsional untuk production)
1. Navigate ke "APIs & Services" > "Credentials"
2. Klik "Create Credentials" > "Service Account"
3. Isi nama service account
4. Download JSON key file
5. Share Google Sheets dengan email service account

#### c. Buat API Key
1. Navigate ke "APIs & Services" > "Credentials"
2. Klik "Create Credentials" > "API Key"
3. Copy API key untuk konfigurasi

### 2. OAuth 2.0 Setup (untuk user authentication)

#### a. Buat OAuth 2.0 Client
1. Di Google Cloud Console, navigate ke "Credentials"
2. Klik "Create Credentials" > "OAuth 2.0 Client IDs"
3. Pilih "Web application"
4. Tambahkan authorized redirect URIs:
   - `http://localhost:3000` (development)
   - `https://yourdomain.com` (production)

#### b. Generate OAuth Token
```bash
# Install Google APIs client library
npm install googleapis

# Atau gunakan OAuth 2.0 Playground
# https://developers.google.com/oauthplayground/
```

### 3. Environment Variables

Tambahkan ke file `.env.local`:

```env
# Google Sheets Integration
NEXT_PUBLIC_GOOGLE_API_KEY=your_google_api_key_here
NEXT_PUBLIC_GOOGLE_OAUTH_TOKEN=your_oauth_token_here
NEXT_PUBLIC_MASTER_SPREADSHEET_ID=your_master_spreadsheet_id
NEXT_PUBLIC_PROJECT_TEMPLATE_ID=your_template_spreadsheet_id
NEXT_PUBLIC_PROJECTS_FOLDER_ID=your_google_drive_folder_id
```

### 4. Google Sheets Structure

#### a. Master Spreadsheet
Buat Google Sheets dengan sheet bernama "Projects" dengan kolom:

| A | B | C | D | E | F | G |
|---|---|---|---|---|---|---|
| ID | Nama Project | Deskripsi | Status | Tanggal Dibuat | Sheet URL | Created At |

#### b. Project Template
Buat template Google Sheets dengan sheet bernama "Checklist" dengan struktur:

```
Row 1: Project: [Nama Project]
Row 2: ID: [Project ID]
Row 3: Created: [Tanggal]
Row 4: [kosong]
Row 5: ID | Indikator | Checklist Item | Status | Priority | Tanggal Selesai | Dokumen Link
Row 6+: [data checklist]
```

### 5. Google Apps Script Setup

#### a. Buat Google Apps Script Project
1. Buka [Google Apps Script](https://script.google.com/)
2. Klik "New Project"
3. Copy code dari `scripts/google-apps-script-automation.js`
4. Update konfigurasi:
   ```javascript
   const SUPABASE_URL = 'https://your-project.supabase.co';
   const SUPABASE_KEY = 'your_supabase_anon_key';
   ```

#### b. Setup Triggers
1. **onEdit Trigger**: Otomatis dibuat saat save script (tidak perlu manual setup)
2. Authorize aplikasi untuk mengakses spreadsheet saat diminta
3. Untuk test: edit cell di kolom Status (kolom D) di sheet "Checklist"
4. **Optional**: Jalankan `setupPeriodicSync()` untuk sync berkala setiap 5 menit

#### c. Test Connection
1. Jalankan function `testSupabaseConnection()`
2. Pastikan menampilkan "✅ Supabase connection successful!"

## Penggunaan

### 1. Membuat Project Baru
- Buat project di aplikasi Next.js
- Google Sheet otomatis dibuat
- URL sheet tersimpan di database

### 2. Kolaborasi Tim
- Bagikan Google Sheet URL ke tim
- Tim edit status checklist langsung di Google Sheets
- Gunakan dropdown untuk status: "Pending", "Progress", "Selesai"

### 3. Sinkronisasi Manual
- Klik tombol "Sinkronisasi" di aplikasi
- Atau jalankan `manualSyncAll()` di Google Apps Script

## API Endpoints

### GET /api/sync-google-sheets
Cek konfigurasi Google Sheets

**Response:**
```json
{
  "configured": true,
  "message": "Google Sheets integration is properly configured"
}
```

### POST /api/sync-google-sheets

#### Action: sync-checklist
Sinkronisasi checklist dari Google Sheets ke Supabase

**Request:**
```json
{
  "projectId": 123,
  "sheetId": "1abc123...",
  "action": "sync-checklist"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Checklist synchronized successfully",
  "data": {
    "totalItems": 10,
    "successCount": 9,
    "errorCount": 1
  }
}
```

#### Action: create-sheet
Buat Google Sheet baru untuk project

**Request:**
```json
{
  "projectId": 123,
  "action": "create-sheet"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Google Sheet created successfully",
  "sheetUrl": "https://docs.google.com/spreadsheets/d/..."
}
```

#### Action: get-sheet-url
Ambil URL Google Sheet untuk project

**Request:**
```json
{
  "projectId": 123,
  "action": "get-sheet-url"
}
```

**Response:**
```json
{
  "success": true,
  "sheetUrl": "https://docs.google.com/spreadsheets/d/..."
}
```

## Troubleshooting

### 1. Google Sheets API Error
**Error:** "Google Sheets API error: 403"
**Solution:** 
- Pastikan Google Sheets API sudah diaktifkan
- Cek API quotas di Google Cloud Console
- Verify API key permissions

### 2. OAuth Token Expired
**Error:** "OAuth token expired"
**Solution:**
- Generate OAuth token baru
- Update environment variable
- Restart aplikasi

### 3. Sync Gagal
**Error:** "Failed to sync with Google Sheets"
**Solution:**
- Cek network connectivity
- Verify Supabase URL dan API key
- Pastikan Google Apps Script triggers aktif

### 4. Sheet Not Found
**Error:** "Invalid Google Sheet URL"
**Solution:**
- Pastikan Google Sheet accessible
- Cek sharing permissions
- Verify sheet ID format

### 5. Google Apps Script Errors

#### Error: "ScriptApp.newTrigger(...).onEdit is not a function"
**Solution:**
- onEdit triggers dibuat otomatis oleh Google Apps Script
- Tidak perlu manual trigger setup
- Cukup save script dan trigger akan aktif

#### Error: "Authorization required"
**Solution:**
- Jalankan function `testSupabaseConnection()` 
- Accept permissions saat diminta
- Re-run function setelah authorization

#### Error: "onEdit function not triggering"
**Solution:**
- Pastikan function bernama exactly `onEdit`
- Check bahwa script di-attach ke spreadsheet yang benar
- Edit cell di kolom D (Status) untuk test
- Check execution logs: Extensions > Apps Script > Executions

#### Error: "Supabase connection failed"
**Solution:**
- Verify SUPABASE_URL dan SUPABASE_KEY di script
- Check Supabase RLS policies
- Test koneksi dengan function `testSupabaseConnection()`

## Security Best Practices

### 1. API Key Protection
- Jangan commit API keys ke repository
- Gunakan environment variables
- Rotate API keys secara berkala

### 2. Sheet Permissions
- Set minimal required permissions
- Gunakan viewer/editor roles appropriately
- Monitor access logs

### 3. Data Validation
- Validate input data di Google Apps Script
- Implement rate limiting
- Log all sync activities

## Performance Optimization

### 1. Batch Operations
- Sync multiple items dalam satu request
- Implement debouncing untuk frequent changes
- Use bulk update APIs

### 2. Caching
- Cache Google Sheets data
- Implement smart refresh strategies
- Use ETags untuk conditional requests

### 3. Error Handling
- Implement retry logic
- Graceful degradation saat Google Sheets tidak available
- Queue failed sync operations

## Monitoring dan Logging

### 1. Google Apps Script Logs
- Monitor execution transcript
- Set up email notifications untuk errors
- Track sync frequency dan success rate

### 2. Application Monitoring
- Log all Google Sheets API calls
- Monitor sync success/failure rates
- Track user collaboration patterns

### 3. Database Monitoring
- Monitor checklist status changes
- Track data consistency
- Alert pada sync failures

## Roadmap

### Future Enhancements
- [ ] Real-time sync menggunakan webhooks
- [ ] Offline support dengan conflict resolution
- [ ] Advanced permissions management
- [ ] Integration dengan Google Docs untuk dokumentasi
- [ ] Mobile app support
- [ ] Advanced analytics dan reporting

## Support

Untuk pertanyaan dan bantuan teknis, silakan:
1. Check troubleshooting guide di atas
2. Review Google Sheets API documentation
3. Contact tim development

---

**Note:** Pastikan untuk selalu backup data sebelum melakukan perubahan konfigurasi major. 