-- Sample Data: Indikator Penilaian Inovasi Sosial 2024
-- Berdasarkan dokumen yang diberikan user

-- Insert project Inovasi Sosial 2024
INSERT INTO public.projects (name, description, status, assignee, google_sheet_url) VALUES
('Indikator Penilaian Inovasi Sosial 2024', 'Program penilaian dan evaluasi inovasi sosial dengan fokus pada kelompok rentan dan marginal', 'active', 'Tim Inovasi Sosial', NULL);

-- Get project ID untuk referensi
-- Untuk keperluan script ini, kita akan menggunakan subquery

-- Insert indicators utama
INSERT INTO public.indicators (project_id, name, description) VALUES
((SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024'), 'Terdapat Intisari', 'Terdapat Pendahuluan, dengan rincian sebagai berikut'),
((SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024'), 'Inovasi Sosial Kelompok Rentan', 'Inovasi sosial harus menjawab kebutuhan kelompok rentan dan marginal'),
((SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024'), 'Terdapat Perubahan Kemampuan', 'Terdapat perubahan kemampuan atau aksesibilitas kelompok rentan yang terlibat');

-- Insert checklist items untuk Indikator 1: Terdapat Intisari
INSERT INTO public.checklist_items (indicator_id, description, status, priority, completed_at) VALUES
-- Sub-indikator Terdapat Intisari
((SELECT id FROM public.indicators WHERE name = 'Terdapat Intisari' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Latar belakang program inovasi sosial', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Terdapat Intisari' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Deskripsi program inovasi sosial', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Terdapat Intisari' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Perubahan signifikan yang dihasilkan (outstanding achievement)', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Terdapat Intisari' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Road map program inovasi sosial - Dibuat minimal untuk 5 tahun', FALSE, 'medium', NULL),
((SELECT id FROM public.indicators WHERE name = 'Terdapat Intisari' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Road map program inovasi sosial - Saat ini sudah berjalan di tahun ke-3', FALSE, 'medium', NULL),
((SELECT id FROM public.indicators WHERE name = 'Terdapat Intisari' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Road map program inovasi sosial - Dan di akhir tahun harus sudah ada resources center (learning center)', FALSE, 'medium', NULL);

-- Insert checklist items untuk Indikator 2: Inovasi Sosial Kelompok Rentan
INSERT INTO public.checklist_items (indicator_id, description, status, priority, completed_at) VALUES
-- Target kelompok rentan
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Lanjut usia', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Anak-anak', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Fakir miskin', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Wanita hamil', FALSE, 'medium', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Difabel', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Pengungsi karena konflik atau bencana', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Pengangguran', FALSE, 'medium', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Dan lain-lain', FALSE, 'low', NULL),

-- Persyaratan kelompok minimal
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Dengan mendats minimal 100 orang kelompok rentan dan marginal', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Data kelompok rentan - Nama', FALSE, 'medium', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Data kelompok rentan - Usia', FALSE, 'medium', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Data kelompok rentan - Alamat', FALSE, 'medium', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Data kelompok rentan - Deskripsi Kerentanannya', FALSE, 'medium', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Ada Upaya mobilisasi kelompok lain (termasuk masyarakat yang telah sejahtera) untuk peduli kepada kelompok rentan dan marginal tersebut', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Inovasi Sosial Kelompok Rentan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Data perubahan kemampuan yang dirasakan oleh sasaran inovasi sosial harus ada klasifikasinya', FALSE, 'high', NULL);

-- Insert checklist items untuk Indikator 3: Terdapat Perubahan Kemampuan
INSERT INTO public.checklist_items (indicator_id, description, status, priority, completed_at) VALUES
-- Jenis-jenis capital
((SELECT id FROM public.indicators WHERE name = 'Terdapat Perubahan Kemampuan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Intellectual capital', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Terdapat Perubahan Kemampuan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Social capital', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Terdapat Perubahan Kemampuan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Economy capital', FALSE, 'high', NULL),
((SELECT id FROM public.indicators WHERE name = 'Terdapat Perubahan Kemampuan' AND project_id = (SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024')), 'Individual capital', FALSE, 'high', NULL);

-- Insert sample documents untuk project ini
INSERT INTO public.documents (project_id, file_name, file_url, file_size, file_type, uploaded_by) VALUES
((SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024'), 'Dokumen Indikator Penilaian Inovasi Sosial 2024.pdf', '/documents/indikator-inovasi-sosial-2024.pdf', '1.2 MB', 'pdf', 'Tim Inovasi Sosial'),
((SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024'), 'Template Data Kelompok Rentan.xlsx', '/documents/template-data-kelompok-rentan.xlsx', '850 KB', 'excel', 'Tim Inovasi Sosial'),
((SELECT id FROM public.projects WHERE name = 'Indikator Penilaian Inovasi Sosial 2024'), 'Road Map Program 5 Tahun.docx', '/documents/roadmap-program-5tahun.docx', '2.1 MB', 'word', 'Tim Inovasi Sosial'); 