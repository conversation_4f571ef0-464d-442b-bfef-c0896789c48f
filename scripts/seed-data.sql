-- Insert sample data for the checklist management system

-- Insert sample projects
INSERT INTO projects (name, description, status, assignee) VALUES
('Website Redesign', 'Redesign website perusahaan dengan UI/UX yang lebih modern', 'active', '<PERSON>'),
('Mobile App Development', 'Pengembangan aplikasi mobile untuk iOS dan Android', 'active', '<PERSON>'),
('Database Migration', 'Migrasi database dari MySQL ke PostgreSQL', 'active', '<PERSON>'),
('Security Audit', 'Audit keamanan sistem dan infrastruktur', 'pending', '<PERSON> Brown');

-- Insert sample indicators
INSERT INTO indicators (project_id, name, description) VALUES
((SELECT id FROM projects WHERE name = 'Website Redesign'), 'Perencanaan', 'Tahap perencanaan dan analisis'),
((SELECT id FROM projects WHERE name = 'Website Redesign'), 'Desain', 'Tahap desain UI/UX'),
((SELECT id FROM projects WHERE name = 'Website Redesign'), 'Development', 'Tahap pengembangan'),
((SELECT id FROM projects WHERE name = 'Mobile App Development'), 'Perencanaan', 'Tahap perencanaan aplikasi'),
((SELECT id FROM projects WHERE name = 'Mobile App Development'), 'Desain', 'Tahap desain aplikasi'),
((SELECT id FROM projects WHERE name = 'Mobile App Development'), 'Development', 'Tahap pengembangan aplikasi'),
((SELECT id FROM projects WHERE name = 'Database Migration'), 'Perencanaan', 'Perencanaan migrasi'),
((SELECT id FROM projects WHERE name = 'Database Migration'), 'Development', 'Eksekusi migrasi'),
((SELECT id FROM projects WHERE name = 'Database Migration'), 'Testing', 'Testing hasil migrasi');

-- Insert sample checklist items
INSERT INTO checklist_items (indicator_id, description, status, priority, completed_at) VALUES
-- Website Redesign - Perencanaan
((SELECT id FROM indicators WHERE name = 'Perencanaan' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'Analisis kebutuhan bisnis', TRUE, 'high', '2024-01-16'),
((SELECT id FROM indicators WHERE name = 'Perencanaan' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'Riset kompetitor', TRUE, 'medium', '2024-01-17'),
((SELECT id FROM indicators WHERE name = 'Perencanaan' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'Wireframe dan mockup', TRUE, 'high', '2024-01-20'),
((SELECT id FROM indicators WHERE name = 'Perencanaan' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'Approval dari stakeholder', FALSE, 'high', NULL),

-- Website Redesign - Desain
((SELECT id FROM indicators WHERE name = 'Desain' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'UI Design homepage', TRUE, 'high', '2024-01-22'),
((SELECT id FROM indicators WHERE name = 'Desain' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'UI Design halaman produk', TRUE, 'medium', '2024-01-25'),
((SELECT id FROM indicators WHERE name = 'Desain' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'UI Design halaman kontak', FALSE, 'medium', NULL),
((SELECT id FROM indicators WHERE name = 'Desain' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'Design system dan style guide', FALSE, 'low', NULL),

-- Website Redesign - Development
((SELECT id FROM indicators WHERE name = 'Development' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'Setup environment development', TRUE, 'high', '2024-01-28'),
((SELECT id FROM indicators WHERE name = 'Development' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'Frontend development', FALSE, 'high', NULL),
((SELECT id FROM indicators WHERE name = 'Development' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'Backend API development', FALSE, 'high', NULL),
((SELECT id FROM indicators WHERE name = 'Development' AND project_id = (SELECT id FROM projects WHERE name = 'Website Redesign')), 'Database setup', FALSE, 'medium', NULL);

-- Insert sample documents
INSERT INTO documents (project_id, name, file_url, file_size, file_type, uploaded_by) VALUES
((SELECT id FROM projects WHERE name = 'Website Redesign'), 'Requirements Document.pdf', '/documents/requirements.pdf', '2.5 MB', 'pdf', 'John Doe'),
((SELECT id FROM projects WHERE name = 'Website Redesign'), 'Wireframe Design.fig', '/documents/wireframe.fig', '15.2 MB', 'figma', 'Jane Smith'),
((SELECT id FROM projects WHERE name = 'Website Redesign'), 'Competitor Analysis.xlsx', '/documents/analysis.xlsx', '1.8 MB', 'excel', 'Bob Johnson'),
((SELECT id FROM projects WHERE name = 'Mobile App Development'), 'API Documentation.docx', '/documents/api-docs.docx', '3.2 MB', 'word', 'Alice Brown'),
((SELECT id FROM projects WHERE name = 'Database Migration'), 'Database Schema.sql', '/documents/schema.sql', '0.8 MB', 'sql', 'Charlie Wilson');
