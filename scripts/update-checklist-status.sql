-- Update Checklist Items Table to Support Status Labels
-- Run this in Supabase SQL Editor after the main schema

-- Add status_label column to checklist_items table
ALTER TABLE public.checklist_items 
ADD COLUMN IF NOT EXISTS status_label VARCHAR(20) DEFAULT 'pending';

-- Update existing records to set appropriate status_label based on current status
UPDATE public.checklist_items 
SET status_label = CASE 
  WHEN status = true THEN 'completed'
  ELSE 'pending'
END
WHERE status_label IS NULL OR status_label = '';

-- Add check constraint for valid status_label values
ALTER TABLE public.checklist_items 
ADD CONSTRAINT check_status_label 
CHECK (status_label IN ('pending', 'in_progress', 'completed'));

-- Create index for better performance on status queries
CREATE INDEX IF NOT EXISTS idx_checklist_items_status_label ON public.checklist_items(status_label); 