-- Supabase Schema for Checklist Management System
-- Run this in Supabase SQL Editor

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'active',
    assignee <PERSON><PERSON><PERSON><PERSON>(255),
    google_sheet_url VARCHAR(500), -- For Google Sheets integration
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indicators table
CREATE TABLE IF NOT EXISTS public.indicators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create checklist_items table
CREATE TABLE IF NOT EXISTS public.checklist_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    indicator_id UUID NOT NULL REFERENCES public.indicators(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    status BOOLEAN DEFAULT FALSE,
    priority VARCHAR(20) DEFAULT 'medium',
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create documents table
CREATE TABLE IF NOT EXISTS public.documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_url VARCHAR(500) NOT NULL,
    file_size VARCHAR(50),
    file_type VARCHAR(50),
    uploaded_by VARCHAR(255),
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_indicators_project_id ON public.indicators(project_id);
CREATE INDEX IF NOT EXISTS idx_checklist_items_indicator_id ON public.checklist_items(indicator_id);
CREATE INDEX IF NOT EXISTS idx_documents_project_id ON public.documents(project_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
CREATE INDEX IF NOT EXISTS idx_checklist_items_status ON public.checklist_items(status);

-- Enable Row Level Security (RLS) for all tables
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.indicators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.checklist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust based on your auth requirements)
-- For now, allowing all operations for authenticated users

-- Projects policies
CREATE POLICY "Enable read access for all users" ON public.projects FOR SELECT USING (true);
CREATE POLICY "Enable insert for authenticated users only" ON public.projects FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for authenticated users only" ON public.projects FOR UPDATE USING (true);
CREATE POLICY "Enable delete for authenticated users only" ON public.projects FOR DELETE USING (true);

-- Indicators policies
CREATE POLICY "Enable read access for all users" ON public.indicators FOR SELECT USING (true);
CREATE POLICY "Enable insert for authenticated users only" ON public.indicators FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for authenticated users only" ON public.indicators FOR UPDATE USING (true);
CREATE POLICY "Enable delete for authenticated users only" ON public.indicators FOR DELETE USING (true);

-- Checklist items policies
CREATE POLICY "Enable read access for all users" ON public.checklist_items FOR SELECT USING (true);
CREATE POLICY "Enable insert for authenticated users only" ON public.checklist_items FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for authenticated users only" ON public.checklist_items FOR UPDATE USING (true);
CREATE POLICY "Enable delete for authenticated users only" ON public.checklist_items FOR DELETE USING (true);

-- Documents policies
CREATE POLICY "Enable read access for all users" ON public.documents FOR SELECT USING (true);
CREATE POLICY "Enable insert for authenticated users only" ON public.documents FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update for authenticated users only" ON public.documents FOR UPDATE USING (true);
CREATE POLICY "Enable delete for authenticated users only" ON public.documents FOR DELETE USING (true);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for projects table
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON public.projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 