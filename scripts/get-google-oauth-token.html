<!DOCTYPE html>
<html>
<head>
    <title>Google OAuth Token Generator</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .token-result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 Google OAuth Token Generator</h1>
        <p>Tool sederhana untuk mendapatkan Google OAuth token untuk Google Sheets API</p>

        <div class="step">
            <h3>Step 1: Setup OAuth Client</h3>
            <p>Masukkan OAuth Client credentials dari Google Cloud Console:</p>
            <input type="text" id="clientId" placeholder="OAuth Client ID" />
            <input type="text" id="redirectUri" value="http://localhost:8080/oauth-callback" readonly />
            <small>Pastikan redirect URI ini sudah ditambahkan di Google Cloud Console</small>
        </div>

        <div class="step">
            <h3>Step 2: Generate Authorization URL</h3>
            <button onclick="generateAuthUrl()">Generate Auth URL</button>
            <div id="authUrlResult"></div>
        </div>

        <div class="step">
            <h3>Step 3: Get Authorization Code</h3>
            <p>Setelah authorize, paste authorization code dari URL:</p>
            <input type="text" id="authCode" placeholder="Authorization code" />
        </div>

        <div class="step">
            <h3>Step 4: Exchange for Access Token</h3>
            <input type="text" id="clientSecret" placeholder="OAuth Client Secret" />
            <button onclick="exchangeToken()">Get Access Token</button>
            <div id="tokenResult"></div>
        </div>
    </div>

    <script>
        function generateAuthUrl() {
            const clientId = document.getElementById('clientId').value;
            const redirectUri = document.getElementById('redirectUri').value;
            
            if (!clientId) {
                alert('Please enter Client ID');
                return;
            }

            const scope = 'https://www.googleapis.com/auth/spreadsheets https://www.googleapis.com/auth/drive.file';
            const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
                `client_id=${encodeURIComponent(clientId)}&` +
                `redirect_uri=${encodeURIComponent(redirectUri)}&` +
                `scope=${encodeURIComponent(scope)}&` +
                `response_type=code&` +
                `access_type=offline&` +
                `prompt=consent`;

            document.getElementById('authUrlResult').innerHTML = `
                <div class="token-result">
                    <p><strong>Klik link ini untuk authorize:</strong></p>
                    <a href="${authUrl}" target="_blank" style="word-break: break-all;">${authUrl}</a>
                    <p><small>Setelah authorize, copy "code" parameter dari URL dan paste di Step 3</small></p>
                </div>
            `;
        }

        async function exchangeToken() {
            const clientId = document.getElementById('clientId').value;
            const clientSecret = document.getElementById('clientSecret').value;
            const authCode = document.getElementById('authCode').value;
            const redirectUri = document.getElementById('redirectUri').value;

            if (!clientId || !clientSecret || !authCode) {
                alert('Please fill all fields');
                return;
            }

            try {
                const response = await fetch('https://oauth2.googleapis.com/token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        code: authCode,
                        client_id: clientId,
                        client_secret: clientSecret,
                        redirect_uri: redirectUri,
                        grant_type: 'authorization_code'
                    })
                });

                const tokens = await response.json();

                if (tokens.error) {
                    document.getElementById('tokenResult').innerHTML = `
                        <div class="error">
                            <strong>Error:</strong> ${tokens.error}<br>
                            <strong>Description:</strong> ${tokens.error_description || 'Unknown error'}
                        </div>
                    `;
                    return;
                }

                document.getElementById('tokenResult').innerHTML = `
                    <div class="token-result">
                        <h4>✅ Success! Your OAuth Tokens:</h4>
                        <p><strong>Access Token (untuk NEXT_PUBLIC_GOOGLE_OAUTH_TOKEN):</strong></p>
                        <textarea rows="3" readonly>${tokens.access_token}</textarea>
                        
                        ${tokens.refresh_token ? `
                        <p><strong>Refresh Token (untuk refresh otomatis):</strong></p>
                        <textarea rows="3" readonly>${tokens.refresh_token}</textarea>
                        ` : ''}
                        
                        <p><strong>Expires in:</strong> ${tokens.expires_in} seconds (${Math.round(tokens.expires_in/3600)} hours)</p>
                        
                        <h4>Environment Variables:</h4>
                        <textarea rows="4" readonly>NEXT_PUBLIC_GOOGLE_OAUTH_TOKEN=${tokens.access_token}
${tokens.refresh_token ? `NEXT_PUBLIC_GOOGLE_REFRESH_TOKEN=${tokens.refresh_token}` : ''}</textarea>
                    </div>
                `;
            } catch (error) {
                document.getElementById('tokenResult').innerHTML = `
                    <div class="error">
                        <strong>Error:</strong> ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html> 