-- Google Tokens Table for Secure Token Storage
-- Run this in Supabase SQL Editor

-- Create google_tokens table
CREATE TABLE IF NOT EXISTS public.google_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    access_token TEXT NOT NULL,
    refresh_token TEXT,
    expires_at TIMESTAMP WITH TIME ZONE,
    scopes TEXT[], -- Array of scopes
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_google_tokens_is_active ON public.google_tokens(is_active);
CREATE INDEX IF NOT EXISTS idx_google_tokens_expires_at ON public.google_tokens(expires_at);

-- Enable Row Level Security (RLS)
ALTER TABLE public.google_tokens ENABLE ROW LEVEL SECURITY;

-- Create policy to allow all operations for authenticated users
-- In production, you might want more restrictive policies
CREATE POLICY "Allow all operations for authenticated users" ON public.google_tokens
FOR ALL USING (auth.role() = 'authenticated');

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_google_tokens_updated_at 
    BEFORE UPDATE ON public.google_tokens 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert comment for documentation
COMMENT ON TABLE public.google_tokens IS 'Stores Google OAuth tokens securely';
COMMENT ON COLUMN public.google_tokens.access_token IS 'Google OAuth access token';
COMMENT ON COLUMN public.google_tokens.refresh_token IS 'Google OAuth refresh token for automatic renewal';
COMMENT ON COLUMN public.google_tokens.expires_at IS 'When the access token expires';
COMMENT ON COLUMN public.google_tokens.scopes IS 'Array of OAuth scopes granted';
COMMENT ON COLUMN public.google_tokens.is_active IS 'Whether this token set is currently active';
