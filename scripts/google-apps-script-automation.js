/**
 * Google Apps Script untuk Automasi Checklist Management System
 * 
 * SETUP INSTRUCTIONS:
 * 
 * 1. Buka Google Apps Script (script.google.com)
 * 2. Buat project baru atau buka existing project
 * 3. Delete semua code default, copy paste seluruh code ini
 * 4. Update konfigurasi di bawah:
 *    - SUPABASE_URL: URL Supabase project Anda
 *    - SUPABASE_KEY: Anon key dari Supabase
 * 5. Save project (Ctrl+S atau Cmd+S)
 * 6. Jalankan function testSupabaseConnection() untuk test koneksi
 * 7. onEdit trigger akan otomatis aktif setelah save
 * 
 * IMPORTANT NOTES:
 * - onEdit triggers otomatis dibuat oleh Google Apps Script
 * - Tidak perlu manual setup trigger untuk onEdit
 * - Function onEdit akan jalan otomatis saat ada edit di spreadsheet
 * - Pastikan script ini attach ke spreadsheet yang tepat
 * 
 * TESTING:
 * - Edit cell di kolom Status (kolom D) di sheet "Checklist"
 * - Check logs: View > Logs atau Executions
 * - Verify data sync ke Supabase
 */

// Configuration - Update these values
const SUPABASE_URL = 'YOUR_SUPABASE_URL';
const SUPABASE_KEY = 'YOUR_SUPABASE_ANON_KEY';

/**
 * Main function that triggers when any cell is edited
 * Only responds to changes in the Status column (column D) of Checklist sheet
 */
function onEdit(e) {
  var sheet = e.source.getActiveSheet();
  var range = e.range;
  
  // Only trigger on Checklist sheet and Status column (column D, index 4)
  if (sheet.getName() === "Checklist" && range.getColumn() === 4) {
    var status = range.getValue();
    var row = range.getRow();
    
    // Skip header rows (rows 1-5 are headers)
    if (row < 6) return;
    
    var checklistId = sheet.getRange(row, 1).getValue(); // Column A: ID
    var completedDate = status === "Selesai" ? new Date().toLocaleDateString('id-ID') : "";
    
    // Update completed date in sheet (column F)
    if (status === "Selesai") {
      sheet.getRange(row, 6).setValue(completedDate);
    } else if (status === "Pending") {
      sheet.getRange(row, 6).setValue(""); // Clear completed date
    }
    
    console.log(`Status updated for checklist ID ${checklistId}: ${status}`);
    
    // Sync to Supabase
    syncToSupabase(checklistId, status, completedDate);
  }
}

/**
 * Sync checklist item status to Supabase
 */
function syncToSupabase(checklistId, status, completedDate) {
  var payload = {
    "status": status === "Selesai",
    "status_label": status === "Selesai" ? "completed" : 
                   status === "Progress" ? "in_progress" : "pending",
    "completed_at": status === "Selesai" ? new Date().toISOString() : null
  };

  var options = {
    "method": "PATCH",
    "contentType": "application/json",
    "payload": JSON.stringify(payload),
    "headers": {
      "apikey": SUPABASE_KEY,
      "Authorization": "Bearer " + SUPABASE_KEY,
      "Content-Type": "application/json"
    }
  };

  try {
    var response = UrlFetchApp.fetch(
      SUPABASE_URL + "/rest/v1/checklist_items?id=eq." + checklistId, 
      options
    );
    
    if (response.getResponseCode() === 200 || response.getResponseCode() === 204) {
      console.log("Successfully synced to Supabase:", checklistId);
    } else {
      console.error("Failed to sync to Supabase:", response.getResponseCode(), response.getContentText());
    }
  } catch (error) {
    console.error("Error syncing to Supabase:", error);
  }
}

/**
 * Manual sync function to sync all checklist items at once
 * Useful for initial setup or bulk updates
 */
function manualSyncAll() {
  var sheet = SpreadsheetApp.getActiveSheet();
  
  // Ensure we're on the Checklist sheet
  if (sheet.getName() !== "Checklist") {
    Browser.msgBox("Please run this function from the Checklist sheet");
    return;
  }
  
  var data = sheet.getRange("A6:G1000").getValues(); // Start from row 6 (after headers)
  var syncCount = 0;
  var errorCount = 0;
  
  for (var i = 0; i < data.length; i++) {
    var row = data[i];
    
    // Check if row has data (ID, indicator, and item description)
    if (row[0] && row[1] && row[2]) {
      var checklistId = row[0];  // Column A: ID
      var status = row[3];       // Column D: Status
      var completedDate = row[5]; // Column F: Completed Date
      
      try {
        syncToSupabase(checklistId, status, completedDate);
        syncCount++;
      } catch (error) {
        console.error("Error syncing item " + checklistId + ":", error);
        errorCount++;
      }
    }
  }
  
  Browser.msgBox("Sync completed: " + syncCount + " items synced, " + errorCount + " errors");
}

/**
 * Setup function to create triggers
 * Run this once to set up automatic sync
 */
function setupTriggers() {
  // Delete existing triggers
  var triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(function(trigger) {
    if (trigger.getHandlerFunction() === 'onEdit') {
      ScriptApp.deleteTrigger(trigger);
    }
  });
  
  // Note: onEdit triggers are automatically created when you save the script
  // Manual trigger creation is not needed for onEdit events
  // This function is kept for other potential triggers in the future
  
  Browser.msgBox("onEdit trigger is automatically created when you save this script. No manual setup needed.");
}

/**
 * Alternative setup for time-based triggers (if needed for periodic sync)
 */
function setupPeriodicSync() {
  // Delete existing time-based triggers
  var triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(function(trigger) {
    if (trigger.getHandlerFunction() === 'periodicSync') {
      ScriptApp.deleteTrigger(trigger);
    }
  });
  
  // Create time-based trigger for periodic sync (every 5 minutes)
  ScriptApp.newTrigger('periodicSync')
    .timeBased()
    .everyMinutes(5)
    .create();
    
  Browser.msgBox("Periodic sync trigger setup completed. Will sync every 5 minutes.");
}

/**
 * Periodic sync function (optional)
 * This will run every 5 minutes if setupPeriodicSync() is executed
 */
function periodicSync() {
  // Only run if we're on a project sheet (not the master sheet)
  var sheet = SpreadsheetApp.getActiveSheet();
  if (sheet.getName() !== "Checklist") {
    return;
  }
  
  console.log("Running periodic sync...");
  // You can add logic here to check for changes and sync if needed
}

/**
 * Test function to validate Supabase connection
 */
function testSupabaseConnection() {
  var options = {
    "method": "GET",
    "headers": {
      "apikey": SUPABASE_KEY,
      "Authorization": "Bearer " + SUPABASE_KEY
    }
  };

  try {
    var response = UrlFetchApp.fetch(
      SUPABASE_URL + "/rest/v1/checklist_items?limit=1", 
      options
    );
    
    if (response.getResponseCode() === 200) {
      Browser.msgBox("✅ Supabase connection successful!");
    } else {
      Browser.msgBox("❌ Supabase connection failed: " + response.getResponseCode());
    }
  } catch (error) {
    Browser.msgBox("❌ Error connecting to Supabase: " + error);
  }
}

/**
 * Helper function to create a new project sheet from template
 * This would be called by the master sheet when a new project is added
 */
function createNewProjectSheet() {
  var ss = SpreadsheetApp.getActiveSpreadsheet();
  var projectsSheet = ss.getSheetByName("Projects");

  if (!projectsSheet) {
    Browser.msgBox("Projects sheet not found. Please create a 'Projects' sheet first.");
    return;
  }

  var lastRow = projectsSheet.getLastRow();
  if (lastRow < 2) {
    Browser.msgBox("No project data found in Projects sheet.");
    return;
  }

  var projectId = projectsSheet.getRange(lastRow, 1).getValue();
  var projectName = projectsSheet.getRange(lastRow, 2).getValue();

  // Configuration for template and folder - update these IDs
  var templateSheetId = "YOUR_TEMPLATE_SHEET_ID";
  var folderId = "YOUR_PROJECT_FOLDER_ID";

  try {
    var folder = DriveApp.getFolderById(folderId);
    var templateFile = DriveApp.getFileById(templateSheetId);
    var newFile = templateFile.makeCopy(projectName + " - Checklist", folder);
    var newSheetUrl = newFile.getUrl();

    // Update the Projects sheet with the new sheet URL (column F)
    projectsSheet.getRange(lastRow, 6).setValue(newSheetUrl);

    Browser.msgBox("✅ New project sheet created: " + newSheetUrl);
    
    // Optionally, send the URL back to Supabase
    updateProjectSheetUrlInSupabase(projectId, newSheetUrl);
    
  } catch (error) {
    Browser.msgBox("❌ Error creating project sheet: " + error);
  }
}

/**
 * Update project sheet URL in Supabase
 */
function updateProjectSheetUrlInSupabase(projectId, sheetUrl) {
  var payload = {
    "google_sheet_url": sheetUrl
  };

  var options = {
    "method": "PATCH",
    "contentType": "application/json",
    "payload": JSON.stringify(payload),
    "headers": {
      "apikey": SUPABASE_KEY,
      "Authorization": "Bearer " + SUPABASE_KEY,
      "Content-Type": "application/json"
    }
  };

  try {
    var response = UrlFetchApp.fetch(
      SUPABASE_URL + "/rest/v1/projects?id=eq." + projectId, 
      options
    );
    
    if (response.getResponseCode() === 200 || response.getResponseCode() === 204) {
      console.log("Successfully updated project sheet URL in Supabase");
    } else {
      console.error("Failed to update project URL in Supabase:", response.getContentText());
    }
  } catch (error) {
    console.error("Error updating project URL in Supabase:", error);
  }
} 